/* نظام إدارة فيش الراتب الجزائري - الأنماط الرئيسية */
/* Algerian Payroll Management System - Main Styles */

:root {
    --primary-color: #2c5530;
    --secondary-color: #4a7c59;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* الخطوط */
body {
    font-family: 'Cairo', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* التنقل */
.navbar {
    box-shadow: var(--shadow);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* الأزرار */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 4px 6px rgba(44, 85, 48, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e3a21, #3a6247);
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(44, 85, 48, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

/* قسم البطل */
.hero-section {
    background: linear-gradient(135deg, rgba(44, 85, 48, 0.1), rgba(74, 124, 89, 0.1));
    border-radius: 1rem;
    margin-bottom: 2rem;
}

.hero-section h1 {
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-section .lead {
    color: #666;
    font-size: 1.2rem;
}

/* الجداول */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(44, 85, 48, 0.05);
}

.table td {
    vertical-align: middle;
    border-color: var(--border-color);
}

/* النماذج */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: var(--light-color);
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(111, 66, 193, 0.1));
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* التقدم */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--light-color);
}

.progress-bar {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* الأيقونات */
.fas, .far, .fab {
    margin-inline-end: 0.5rem;
}

/* الفواصل */
.divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 2rem 0;
}

/* التذييل */
footer {
    background: linear-gradient(135deg, var(--dark-color), #495057) !important;
    margin-top: auto;
}

footer h6 {
    color: var(--secondary-color);
    font-weight: 700;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* الاستجابة */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section .lead {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .hero-section {
        padding: 2rem 1rem !important;
    }
    
    .hero-section h1 {
        font-size: 1.75rem;
    }
}

/* طباعة */
@media print {
    .navbar,
    .btn,
    footer,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table thead th {
        background: #f8f9fa !important;
        color: black !important;
    }
}

/* تخصيصات إضافية */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.rounded-custom {
    border-radius: 0.75rem;
}

/* تحسينات الأداء */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

.container,
.container-fluid {
    transition: all 0.3s ease;
}

/* تحسينات إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --dark-color: #f7fafc;
        --border-color: #4a5568;
    }
    
    body {
        background-color: #1a202c;
        color: #f7fafc;
    }
    
    .card {
        background-color: #2d3748;
        color: #f7fafc;
    }
    
    .table {
        color: #f7fafc;
    }
    
    .form-control,
    .form-select {
        background-color: #2d3748;
        color: #f7fafc;
        border-color: #4a5568;
    }
}
