/**
 * نظام إدارة فيش الراتب الجزائري - JavaScript الرئيسي
 * Algerian Payroll Management System - Main JavaScript
 */

// إعدادات عامة
const APP_CONFIG = {
    language: document.documentElement.lang || 'ar',
    isRTL: document.documentElement.dir === 'rtl',
    currency: 'DZD',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: {
        decimal: '.',
        thousands: ',',
        precision: 2
    }
};

// دالة التهيئة الرئيسية
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    setupTooltips();
    setupFormValidation();
    setupDataTables();
    setupCharts();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تعيين اللغة
    setLanguage(APP_CONFIG.language);
    
    // تحديث التوقيت
    updateDateTime();
    setInterval(updateDateTime, 60000); // كل دقيقة
    
    // تحميل البيانات الأولية
    loadInitialData();
    
    // إعداد الرسوم المتحركة
    setupAnimations();
    
    console.log('تم تهيئة التطبيق بنجاح');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // تبديل اللغة
    document.querySelectorAll('[data-lang]').forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const lang = this.getAttribute('data-lang');
            changeLanguage(lang);
        });
    });
    
    // أزرار الطباعة
    document.querySelectorAll('.print-btn').forEach(button => {
        button.addEventListener('click', function() {
            window.print();
        });
    });
    
    // أزرار التصدير
    document.querySelectorAll('.export-btn').forEach(button => {
        button.addEventListener('click', function() {
            const format = this.getAttribute('data-format');
            exportData(format);
        });
    });
    
    // تأكيد الحذف
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const message = APP_CONFIG.language === 'ar' 
                ? 'هل أنت متأكد من الحذف؟' 
                : 'Êtes-vous sûr de vouloir supprimer?';
            
            if (confirm(message)) {
                window.location.href = this.href;
            }
        });
    });
    
    // حفظ تلقائي للنماذج
    document.querySelectorAll('form[data-autosave]').forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                autoSaveForm(form);
            });
        });
    });
}

/**
 * إعداد التلميحات
 */
function setupTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * إعداد التحقق من النماذج
 */
function setupFormValidation() {
    // التحقق من النماذج باستخدام Bootstrap
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // التحقق المخصص للحقول
    setupCustomValidation();
}

/**
 * التحقق المخصص للحقول
 */
function setupCustomValidation() {
    // التحقق من رقم الهوية الوطنية
    document.querySelectorAll('input[data-validate="national-id"]').forEach(input => {
        input.addEventListener('blur', function() {
            validateNationalId(this);
        });
    });
    
    // التحقق من رقم الضمان الاجتماعي
    document.querySelectorAll('input[data-validate="social-security"]').forEach(input => {
        input.addEventListener('blur', function() {
            validateSocialSecurity(this);
        });
    });
    
    // التحقق من المبالغ المالية
    document.querySelectorAll('input[data-validate="currency"]').forEach(input => {
        input.addEventListener('blur', function() {
            validateCurrency(this);
        });
        
        input.addEventListener('input', function() {
            formatCurrency(this);
        });
    });
}

/**
 * إعداد الجداول
 */
function setupDataTables() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            language: getDataTableLanguage(),
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                {
                    targets: 'no-sort',
                    orderable: false
                },
                {
                    targets: 'currency',
                    render: function(data, type, row) {
                        return formatNumber(data) + ' DA';
                    }
                },
                {
                    targets: 'date',
                    render: function(data, type, row) {
                        return formatDate(data);
                    }
                }
            ]
        });
    }
}

/**
 * إعداد الرسوم البيانية
 */
function setupCharts() {
    // رسم بياني للرواتب الشهرية
    const salaryChartCanvas = document.getElementById('salaryChart');
    if (salaryChartCanvas && typeof Chart !== 'undefined') {
        const ctx = salaryChartCanvas.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: getMonthLabels(),
                datasets: [{
                    label: APP_CONFIG.language === 'ar' ? 'إجمالي الرواتب' : 'Total Salaires',
                    data: [0, 0, 0, 0, 0, 0], // سيتم تحديثها من الخادم
                    borderColor: '#2c5530',
                    backgroundColor: 'rgba(44, 85, 48, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatNumber(value) + ' DA';
                            }
                        }
                    }
                }
            }
        });
    }
}

/**
 * إعداد الرسوم المتحركة
 */
function setupAnimations() {
    // رسوم متحركة عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        observer.observe(element);
    });
}

/**
 * تغيير اللغة
 */
function changeLanguage(lang) {
    const url = new URL(window.location);
    url.searchParams.set('lang', lang);
    window.location.href = url.toString();
}

/**
 * تعيين اللغة
 */
function setLanguage(lang) {
    APP_CONFIG.language = lang;
    APP_CONFIG.isRTL = lang === 'ar';
    
    // تحديث اتجاه الصفحة
    document.documentElement.dir = APP_CONFIG.isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    const now = new Date();
    const dateTimeElements = document.querySelectorAll('.current-datetime');
    
    dateTimeElements.forEach(element => {
        element.textContent = formatDateTime(now);
    });
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime(date) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return date.toLocaleDateString(APP_CONFIG.language === 'ar' ? 'ar-DZ' : 'fr-DZ', options);
}

/**
 * تنسيق التاريخ
 */
function formatDate(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    
    return d.toLocaleDateString(APP_CONFIG.language === 'ar' ? 'ar-DZ' : 'fr-DZ', options);
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, precision = 2) {
    if (isNaN(number)) return '0.00';
    
    return parseFloat(number).toLocaleString(
        APP_CONFIG.language === 'ar' ? 'ar-DZ' : 'fr-DZ',
        {
            minimumFractionDigits: precision,
            maximumFractionDigits: precision
        }
    );
}

/**
 * تنسيق العملة
 */
function formatCurrency(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    if (value) {
        const number = parseFloat(value);
        if (!isNaN(number)) {
            input.value = formatNumber(number);
        }
    }
}

/**
 * التحقق من رقم الهوية الوطنية
 */
function validateNationalId(input) {
    const value = input.value.trim();
    const isValid = /^\d{18}$/.test(value);
    
    setValidationState(input, isValid, 
        APP_CONFIG.language === 'ar' ? 'رقم الهوية غير صحيح' : 'Numéro d\'identité invalide'
    );
    
    return isValid;
}

/**
 * التحقق من رقم الضمان الاجتماعي
 */
function validateSocialSecurity(input) {
    const value = input.value.trim();
    const isValid = /^\d{15,20}$/.test(value);
    
    setValidationState(input, isValid,
        APP_CONFIG.language === 'ar' ? 'رقم الضمان الاجتماعي غير صحيح' : 'Numéro de sécurité sociale invalide'
    );
    
    return isValid;
}

/**
 * التحقق من العملة
 */
function validateCurrency(input) {
    const value = input.value.replace(/[^\d.]/g, '');
    const number = parseFloat(value);
    const isValid = !isNaN(number) && number >= 0;
    
    setValidationState(input, isValid,
        APP_CONFIG.language === 'ar' ? 'المبلغ غير صحيح' : 'Montant invalide'
    );
    
    return isValid;
}

/**
 * تعيين حالة التحقق
 */
function setValidationState(input, isValid, message) {
    const feedback = input.parentNode.querySelector('.invalid-feedback');
    
    if (isValid) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
    } else {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');
        if (feedback) {
            feedback.textContent = message;
        }
    }
}

/**
 * حفظ تلقائي للنموذج
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // حفظ في التخزين المحلي
    localStorage.setItem('autosave_' + form.id, JSON.stringify(data));
    
    // إظهار رسالة الحفظ
    showNotification(
        APP_CONFIG.language === 'ar' ? 'تم الحفظ تلقائياً' : 'Sauvegarde automatique',
        'success',
        2000
    );
}

/**
 * تحميل البيانات الأولية
 */
function loadInitialData() {
    // تحميل الإحصائيات
    loadDashboardStats();
    
    // تحميل البيانات المحفوظة تلقائياً
    loadAutoSavedData();
}

/**
 * تحميل إحصائيات لوحة التحكم
 */
function loadDashboardStats() {
    // سيتم تنفيذها عبر AJAX
    console.log('تحميل الإحصائيات...');
}

/**
 * تحميل البيانات المحفوظة تلقائياً
 */
function loadAutoSavedData() {
    document.querySelectorAll('form[data-autosave]').forEach(form => {
        const savedData = localStorage.getItem('autosave_' + form.id);
        if (savedData) {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    input.value = data[key];
                }
            });
        }
    });
}

/**
 * تصدير البيانات
 */
function exportData(format) {
    const message = APP_CONFIG.language === 'ar' 
        ? `جاري تصدير البيانات بصيغة ${format}...`
        : `Export des données en format ${format}...`;
    
    showNotification(message, 'info');
    
    // سيتم تنفيذ التصدير الفعلي عبر الخادم
    console.log('تصدير البيانات:', format);
}

/**
 * إظهار الإشعارات
 */
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

/**
 * الحصول على تسميات الأشهر
 */
function getMonthLabels() {
    const months = APP_CONFIG.language === 'ar' 
        ? ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
        : ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin'];
    
    return months;
}

/**
 * الحصول على لغة DataTable
 */
function getDataTableLanguage() {
    if (APP_CONFIG.language === 'ar') {
        return {
            "sProcessing": "جاري التحميل...",
            "sLengthMenu": "أظهر _MENU_ مدخل",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sSearch": "ابحث:",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        };
    } else {
        return {
            "sProcessing": "Traitement en cours...",
            "sSearch": "Rechercher :",
            "sLengthMenu": "Afficher _MENU_ éléments",
            "sInfo": "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments",
            "sInfoEmpty": "Affichage de l'élément 0 à 0 sur 0 élément",
            "sInfoFiltered": "(filtré de _MAX_ éléments au total)",
            "sZeroRecords": "Aucun élément à afficher",
            "oPaginate": {
                "sFirst": "Premier",
                "sPrevious": "Précédent",
                "sNext": "Suivant",
                "sLast": "Dernier"
            }
        };
    }
}

// دوال مساعدة إضافية
window.APP = {
    config: APP_CONFIG,
    formatNumber: formatNumber,
    formatDate: formatDate,
    formatDateTime: formatDateTime,
    showNotification: showNotification,
    validateNationalId: validateNationalId,
    validateSocialSecurity: validateSocialSecurity,
    validateCurrency: validateCurrency
};
