{"name": "algerian-payroll-system", "version": "1.0.0", "description": "نظام إدارة فيش الراتب الجزائري - Système de Gestion de Paie Algérien", "main": "assets/js/main.js", "scripts": {"dev": "npm run watch", "build": "npm run build-css && npm run build-js && npm run optimize-images", "watch": "npm run watch-css & npm run watch-js", "build-css": "sass assets/scss:assets/css --style compressed --source-map", "watch-css": "sass assets/scss:assets/css --watch --style expanded --source-map", "build-js": "webpack --mode production", "watch-js": "webpack --mode development --watch", "optimize-images": "imagemin assets/images/**/* --out-dir=assets/images/optimized", "lint-js": "eslint assets/js/**/*.js", "lint-css": "stylelint assets/scss/**/*.scss", "format": "prettier --write assets/js/**/*.js assets/scss/**/*.scss", "test": "jest", "serve": "php -S localhost:8000", "backup": "php scripts/backup.php", "deploy": "npm run build && php scripts/deploy.php"}, "keywords": ["payroll", "algeria", "salary", "hrms", "paie", "algerie", "salaire", "rh"], "author": {"name": "Algerian Payroll Team", "email": "<EMAIL>", "url": "https://www.algerian-payroll.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/algerian-payroll/payroll-management-system.git"}, "bugs": {"url": "https://github.com/algerian-payroll/payroll-management-system/issues"}, "homepage": "https://www.algerian-payroll.com", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "eslint": "^8.50.0", "eslint-config-standard": "^17.1.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^10.0.1", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.0.3", "sass": "^1.69.0", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "stylelint": "^15.10.3", "stylelint-config-standard": "^34.0.0", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "dependencies": {"bootstrap": "^5.3.2", "chart.js": "^4.4.0", "datatables.net": "^1.13.6", "datatables.net-bs5": "^1.13.6", "jquery": "^3.7.1", "@fortawesome/fontawesome-free": "^6.4.2", "moment": "^2.29.4", "sweetalert2": "^11.7.32", "select2": "^4.1.0-rc.0", "flatpickr": "^4.6.13", "dropzone": "^6.0.0-beta.2", "aos": "^2.3.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "config": {"port": 8000, "host": "localhost"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["assets/js/**/*.js", "!assets/js/vendor/**"]}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "jquery": true, "es6": true}, "globals": {"bootstrap": "readonly", "Chart": "readonly", "DataTable": "readonly", "Swal": "readonly"}, "rules": {"no-console": "warn", "no-unused-vars": "warn"}}, "stylelint": {"extends": ["stylelint-config-standard"], "rules": {"indentation": 2, "string-quotes": "single", "no-duplicate-selectors": true, "color-hex-case": "lower", "color-hex-length": "short", "selector-combinator-space-after": "always", "selector-attribute-quotes": "always", "selector-attribute-operator-space-before": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-brackets-space-inside": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "number-leading-zero": "always", "function-url-quotes": "always", "font-family-name-quotes": "always-where-recommended", "comment-whitespace-inside": "always", "rule-empty-line-before": "always-multi-line", "selector-pseudo-element-colon-notation": "double", "selector-pseudo-class-parentheses-space-inside": "never", "media-feature-range-operator-space-before": "always", "media-feature-range-operator-space-after": "always", "media-feature-parentheses-space-inside": "never", "media-feature-colon-space-before": "never", "media-feature-colon-space-after": "always"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100, "bracketSpacing": true, "arrowParens": "avoid"}}