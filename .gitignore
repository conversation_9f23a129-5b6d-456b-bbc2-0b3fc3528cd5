# نظام إدارة فيش الراتب الجزائري - Git Ignore
# Algerian Payroll Management System - Git Ignore

# ملفات التكوين الحساسة / Sensitive configuration files
config/local_config.php
.env
.env.local
.env.production
.env.staging

# ملفات قاعدة البيانات / Database files
*.sql.gz
*.sql.bz2
database/backups/
backups/
*.db
*.sqlite
*.sqlite3

# ملفات السجلات / Log files
logs/
*.log
error_log
access_log
php_errors.log

# ملفات مؤقتة / Temporary files
temp/
tmp/
cache/
*.tmp
*.temp
*.cache

# ملفات الرفع / Upload files
uploads/
files/
documents/
images/user_uploads/

# ملفات النظام / System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ملفات IDE / IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.buildpath
.settings/
*.sublime-project
*.sublime-workspace

# ملفات Node.js / Node.js files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.pnp
.pnp.js

# ملفات Composer / Composer files
vendor/
composer.phar
composer.lock

# ملفات التوزيع / Distribution files
dist/
build/
assets/dist/
public/build/

# ملفات التغطية / Coverage files
coverage/
*.lcov
.nyc_output

# ملفات الاختبار / Test files
.phpunit.result.cache
phpunit.xml
tests/_output/
tests/_support/_generated

# ملفات الأمان / Security files
*.pem
*.key
*.crt
*.csr
private/
secrets/

# ملفات النسخ الاحتياطية / Backup files
*.bak
*.backup
*.old
*.orig
*~

# ملفات الضغط / Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# ملفات التثبيت / Installation files
.installed
install.lock

# ملفات الجلسات / Session files
sessions/
tmp/sessions/

# ملفات التخزين المؤقت / Cache files
storage/cache/
storage/logs/
storage/sessions/
storage/tmp/

# ملفات الإنتاج / Production files
.htpasswd
robots.txt.production
sitemap.xml

# ملفات التطوير / Development files
.env.example.local
docker-compose.override.yml
Vagrantfile.local

# ملفات الأدوات / Tools files
.sass-cache/
*.css.map
*.js.map

# ملفات المحررات / Editor files
*.kate-swp
.*.swp
.*.swo

# ملفات macOS / macOS files
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.TemporaryItems
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# ملفات Windows / Windows files
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# ملفات Linux / Linux files
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ملفات خاصة بالمشروع / Project specific files
reports/generated/
exports/
imports/
migrations/executed/

# ملفات التوثيق المولدة / Generated documentation
docs/generated/
api-docs/
phpdoc/

# ملفات الأدوات الخارجية / External tools files
.phpcs.xml
.php_cs.cache
.phpstan.neon
psalm.xml

# ملفات البيئة المحلية / Local environment files
.local
local/
dev/
development/

# ملفات الشهادات / Certificate files
ssl/
certificates/
*.p12
*.pfx

# ملفات قواعد البيانات المحلية / Local database files
*.sql.local
dump.sql
schema.sql.backup

# ملفات التكامل المستمر / CI files (keep in repo but ignore local overrides)
.github/workflows/*.local.yml
.gitlab-ci.local.yml

# ملفات Docker المحلية / Local Docker files
docker-compose.local.yml
.dockerignore.local
Dockerfile.local

# ملفات الاختبار المحلية / Local test files
phpunit.local.xml
tests/local/
test-results/

# ملفات التحليل / Analysis files
.sonarqube/
sonar-project.properties.local

# ملفات الأداء / Performance files
*.prof
*.trace
xdebug/

# ملفات التصدير / Export files
*.csv.export
*.xlsx.export
*.pdf.export

# ملفات مخصصة للمطور / Developer custom files
TODO.md
NOTES.md
scratch/
playground/
