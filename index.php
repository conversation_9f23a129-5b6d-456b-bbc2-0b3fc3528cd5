<?php
/**
 * نظام إدارة فيش الراتب الجزائري
 * Algerian Payroll Management System
 * 
 * الصفحة الرئيسية
 * Main Index Page
 */

// تحميل ملف التكوين
require_once 'config/config.php';

// بدء الجلسة
session_start();

// تحديد اللغة
$lang = isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES) ? $_GET['lang'] : DEFAULT_LANGUAGE;
$_SESSION['language'] = $lang;

// التحقق من تسجيل الدخول
$isLoggedIn = isset($_SESSION['user_id']);
$user = $isLoggedIn ? $_SESSION['user'] : null;

?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo in_array($lang, RTL_LANGUAGES) ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? APP_NAME : APP_NAME_FR; ?></title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if ($lang === 'ar'): ?>
    <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="<?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>">
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-file-invoice-dollar me-2"></i>
                <?php echo $lang === 'ar' ? 'نظام الرواتب' : 'Système de Paie'; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <?php echo $lang === 'ar' ? 'لوحة التحكم' : 'Tableau de bord'; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>
                            <?php echo $lang === 'ar' ? 'الموظفين' : 'Employés'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="employees/list.php">
                                <?php echo $lang === 'ar' ? 'قائمة الموظفين' : 'Liste des employés'; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="employees/add.php">
                                <?php echo $lang === 'ar' ? 'إضافة موظف' : 'Ajouter employé'; ?>
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            <?php echo $lang === 'ar' ? 'الرواتب' : 'Salaires'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="payroll/list.php">
                                <?php echo $lang === 'ar' ? 'فيش الراتب' : 'Bulletins de paie'; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="payroll/calculate.php">
                                <?php echo $lang === 'ar' ? 'حساب الرواتب' : 'Calculer salaires'; ?>
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            <?php echo $lang === 'ar' ? 'التقارير' : 'Rapports'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports/monthly.php">
                                <?php echo $lang === 'ar' ? 'التقرير الشهري' : 'Rapport mensuel'; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="reports/annual.php">
                                <?php echo $lang === 'ar' ? 'التقرير السنوي' : 'Rapport annuel'; ?>
                            </a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            <?php echo $lang === 'ar' ? 'العربية' : 'Français'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                            <li><a class="dropdown-item" href="?lang=fr">Français</a></li>
                        </ul>
                    </li>
                    
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $user['full_name_' . $lang]; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>
                                <?php echo $lang === 'ar' ? 'الملف الشخصي' : 'Profil'; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                <?php echo $lang === 'ar' ? 'الإعدادات' : 'Paramètres'; ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <?php echo $lang === 'ar' ? 'تسجيل الخروج' : 'Déconnexion'; ?>
                            </a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Connexion'; ?>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <?php if (!$isLoggedIn): ?>
        <!-- Welcome Section for Non-logged Users -->
        <div class="row">
            <div class="col-12">
                <div class="hero-section text-center py-5 mb-5">
                    <div class="container">
                        <h1 class="display-4 fw-bold text-primary mb-4">
                            <?php echo $lang === 'ar' ? 'نظام إدارة فيش الراتب الجزائري' : 'Système de Gestion de Paie Algérien'; ?>
                        </h1>
                        <p class="lead mb-4">
                            <?php echo $lang === 'ar' 
                                ? 'نظام شامل ومتطور لإدارة الرواتب وفقاً للقوانين الجزائرية والضمان الاجتماعي'
                                : 'Système complet et avancé de gestion de paie conforme aux lois algériennes et à la sécurité sociale'; ?>
                        </p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="login.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Se connecter'; ?>
                            </a>
                            <a href="#features" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-info-circle me-2"></i>
                                <?php echo $lang === 'ar' ? 'المزيد من المعلومات' : 'En savoir plus'; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div id="features" class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-5">
                    <?php echo $lang === 'ar' ? 'مميزات النظام' : 'Fonctionnalités du système'; ?>
                </h2>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">
                            <?php echo $lang === 'ar' ? 'حساب دقيق للرواتب' : 'Calcul précis des salaires'; ?>
                        </h5>
                        <p class="card-text">
                            <?php echo $lang === 'ar' 
                                ? 'حساب تلقائي للرواتب والاستقطاعات وفقاً للقوانين الجزائرية'
                                : 'Calcul automatique des salaires et déductions selon les lois algériennes'; ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5 class="card-title">
                            <?php echo $lang === 'ar' ? 'امتثال للقوانين' : 'Conformité légale'; ?>
                        </h5>
                        <p class="card-text">
                            <?php echo $lang === 'ar' 
                                ? 'متوافق مع قوانين الضمان الاجتماعي والضرائب الجزائرية'
                                : 'Conforme aux lois de sécurité sociale et fiscales algériennes'; ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                        <h5 class="card-title">
                            <?php echo $lang === 'ar' ? 'تقارير شاملة' : 'Rapports complets'; ?>
                        </h5>
                        <p class="card-text">
                            <?php echo $lang === 'ar' 
                                ? 'تقارير مفصلة وإحصائيات شاملة لجميع جوانب الرواتب'
                                : 'Rapports détaillés et statistiques complètes pour tous les aspects de la paie'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <?php else: ?>
        <!-- Dashboard for Logged Users -->
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <?php echo $lang === 'ar' ? 'مرحباً، ' . $user['full_name_ar'] : 'Bienvenue, ' . $user['full_name_fr']; ?>
                </h1>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'الموظفين النشطين' : 'Employés actifs'; ?>
                                </h6>
                                <h3 class="mb-0">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'فيش الراتب هذا الشهر' : 'Bulletins ce mois'; ?>
                                </h6>
                                <h3 class="mb-0">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file-invoice-dollar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'إجمالي الرواتب' : 'Total salaires'; ?>
                                </h6>
                                <h3 class="mb-0">0 DA</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'المعاملات المعلقة' : 'Transactions en attente'; ?>
                                </h6>
                                <h3 class="mb-0">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <?php echo $lang === 'ar' ? 'الإجراءات السريعة' : 'Actions rapides'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="employees/add.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                                    <?php echo $lang === 'ar' ? 'إضافة موظف' : 'Ajouter employé'; ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="payroll/calculate.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-calculator fa-2x mb-2"></i>
                                    <?php echo $lang === 'ar' ? 'حساب الرواتب' : 'Calculer salaires'; ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="reports/monthly.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <?php echo $lang === 'ar' ? 'التقارير' : 'Rapports'; ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="settings.php" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-cog fa-2x mb-2"></i>
                                    <?php echo $lang === 'ar' ? 'الإعدادات' : 'Paramètres'; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6><?php echo $lang === 'ar' ? APP_NAME : APP_NAME_FR; ?></h6>
                    <p class="mb-0">
                        <?php echo $lang === 'ar' 
                            ? 'نظام متطور لإدارة الرواتب وفقاً للقوانين الجزائرية'
                            : 'Système avancé de gestion de paie conforme aux lois algériennes'; ?>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <?php echo $lang === 'ar' ? 'الإصدار' : 'Version'; ?> <?php echo APP_VERSION; ?>
                    </p>
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> 
                        <?php echo $lang === 'ar' ? 'جميع الحقوق محفوظة' : 'Tous droits réservés'; ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
