<?php
/**
 * فئة قاعدة البيانات
 * Database Class
 */

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;
    
    private function __construct() {
        $config = getDatabaseConfig();
        $this->host = $config['host'];
        $this->dbname = $config['dbname'];
        $this->username = $config['username'];
        $this->password = $config['password'];
        $this->charset = $config['charset'];
        
        $this->connect();
    }
    
    /**
     * الحصول على مثيل واحد من قاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // تسجيل الاتصال الناجح
            if (LOG_QUERIES) {
                error_log("Database connection established successfully");
            }
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            if (LOG_QUERIES) {
                error_log("Query executed: " . $query . " | Params: " . json_encode($params));
            }
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Select query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }
    
    /**
     * تنفيذ استعلام SELECT لسجل واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            if (LOG_QUERIES) {
                error_log("Query executed: " . $query . " | Params: " . json_encode($params));
            }
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Select one query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($table, $data) {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            
            $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            $stmt = $this->connection->prepare($query);
            $stmt->execute($data);
            
            if (LOG_QUERIES) {
                error_log("Insert query executed: " . $query . " | Data: " . json_encode($data));
            }
            
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            error_log("Insert query failed: " . $e->getMessage());
            throw new Exception("فشل في إدراج البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($table, $data, $where, $whereParams = []) {
        try {
            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "{$key} = :{$key}";
            }
            $setClause = implode(', ', $setClause);
            
            $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
            $params = array_merge($data, $whereParams);
            
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            if (LOG_QUERIES) {
                error_log("Update query executed: " . $query . " | Params: " . json_encode($params));
            }
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Update query failed: " . $e->getMessage());
            throw new Exception("فشل في تحديث البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($table, $where, $params = []) {
        try {
            $query = "DELETE FROM {$table} WHERE {$where}";
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            if (LOG_QUERIES) {
                error_log("Delete query executed: " . $query . " | Params: " . json_encode($params));
            }
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Delete query failed: " . $e->getMessage());
            throw new Exception("فشل في حذف البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام مخصص
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            if (LOG_QUERIES) {
                error_log("Custom query executed: " . $query . " | Params: " . json_encode($params));
            }
            
            return $stmt;
        } catch (PDOException $e) {
            error_log("Custom query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * التحقق من وجود جدول
     */
    public function tableExists($tableName) {
        try {
            $query = "SHOW TABLES LIKE :table";
            $stmt = $this->connection->prepare($query);
            $stmt->execute(['table' => $tableName]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من قاعدة البيانات
     */
    public function backup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupPath = BACKUP_PATH . $filename;
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!is_dir(BACKUP_PATH)) {
            mkdir(BACKUP_PATH, 0755, true);
        }
        
        // تنفيذ النسخ الاحتياطي باستخدام mysqldump
        $command = "mysqldump --host={$this->host} --user={$this->username} --password={$this->password} {$this->dbname} > {$backupPath}";
        
        exec($command, $output, $returnVar);
        
        if ($returnVar === 0) {
            return $backupPath;
        } else {
            throw new Exception("فشل في إنشاء النسخة الاحتياطية");
        }
    }
    
    /**
     * استعادة قاعدة البيانات من نسخة احتياطية
     */
    public function restore($backupFile) {
        if (!file_exists($backupFile)) {
            throw new Exception("ملف النسخة الاحتياطية غير موجود");
        }
        
        $command = "mysql --host={$this->host} --user={$this->username} --password={$this->password} {$this->dbname} < {$backupFile}";
        
        exec($command, $output, $returnVar);
        
        if ($returnVar !== 0) {
            throw new Exception("فشل في استعادة النسخة الاحتياطية");
        }
        
        return true;
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    public function cleanOldBackups() {
        if (!is_dir(BACKUP_PATH)) {
            return;
        }
        
        $files = glob(BACKUP_PATH . '*.sql');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $fileTime = filemtime($file);
                $daysDiff = ($now - $fileTime) / (24 * 60 * 60);
                
                if ($daysDiff > BACKUP_RETENTION_DAYS) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * الحصول على إحصائيات قاعدة البيانات
     */
    public function getStats() {
        $stats = [];
        
        // عدد الموظفين
        $result = $this->selectOne("SELECT COUNT(*) as count FROM employees WHERE status = 'active'");
        $stats['active_employees'] = $result['count'];
        
        // عدد الشركات
        $result = $this->selectOne("SELECT COUNT(*) as count FROM companies");
        $stats['companies'] = $result['count'];
        
        // عدد فيش الراتب هذا الشهر
        $result = $this->selectOne("
            SELECT COUNT(*) as count 
            FROM payrolls p 
            JOIN payroll_periods pp ON p.payroll_period_id = pp.id 
            WHERE pp.year = YEAR(NOW()) AND pp.month = MONTH(NOW())
        ");
        $stats['current_month_payrolls'] = $result['count'];
        
        // حجم قاعدة البيانات
        $result = $this->selectOne("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = :dbname
        ", ['dbname' => $this->dbname]);
        $stats['database_size_mb'] = $result['size_mb'];
        
        return $stats;
    }
    
    /**
     * منع الاستنساخ
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
?>
