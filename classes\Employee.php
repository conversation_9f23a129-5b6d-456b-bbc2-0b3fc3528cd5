<?php
/**
 * فئة إدارة الموظفين
 * Employee Management Class
 */

class Employee {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الموظفين
     */
    public function getAllEmployees($filters = []) {
        $where = ['e.status != "deleted"'];
        $params = [];
        
        // تطبيق الفلاتر
        if (!empty($filters['status'])) {
            $where[] = 'e.status = :status';
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['department_id'])) {
            $where[] = 'p.department_id = :department_id';
            $params['department_id'] = $filters['department_id'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(e.first_name_ar LIKE :search OR e.last_name_ar LIKE :search OR e.employee_number LIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $whereClause = implode(' AND ', $where);
        
        $query = "
            SELECT 
                e.*,
                p.title_ar as position_title_ar,
                p.title_fr as position_title_fr,
                p.base_salary,
                d.name_ar as department_name_ar,
                d.name_fr as department_name_fr,
                c.name_ar as company_name_ar,
                c.name_fr as company_name_fr
            FROM employees e
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN companies c ON d.company_id = c.id
            WHERE {$whereClause}
            ORDER BY e.created_at DESC
        ";
        
        return $this->db->select($query, $params);
    }
    
    /**
     * الحصول على موظف بالمعرف
     */
    public function getEmployeeById($id) {
        $query = "
            SELECT 
                e.*,
                p.title_ar as position_title_ar,
                p.title_fr as position_title_fr,
                p.base_salary,
                p.grade_level,
                d.name_ar as department_name_ar,
                d.name_fr as department_name_fr,
                d.id as department_id,
                c.name_ar as company_name_ar,
                c.name_fr as company_name_fr,
                c.id as company_id
            FROM employees e
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN companies c ON d.company_id = c.id
            WHERE e.id = :id AND e.status != 'deleted'
        ";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }
    
    /**
     * الحصول على موظف برقم التسجيل
     */
    public function getEmployeeByNumber($employeeNumber) {
        $query = "
            SELECT 
                e.*,
                p.title_ar as position_title_ar,
                p.title_fr as position_title_fr,
                p.base_salary,
                d.name_ar as department_name_ar,
                d.name_fr as department_name_fr
            FROM employees e
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE e.employee_number = :employee_number AND e.status != 'deleted'
        ";
        
        return $this->db->selectOne($query, ['employee_number' => $employeeNumber]);
    }
    
    /**
     * إضافة موظف جديد
     */
    public function addEmployee($data) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من عدم تكرار رقم التسجيل
            if ($this->employeeNumberExists($data['employee_number'])) {
                throw new Exception('رقم التسجيل موجود بالفعل');
            }
            
            // التحقق من عدم تكرار رقم الهوية
            if ($this->nationalIdExists($data['national_id'])) {
                throw new Exception('رقم الهوية الوطنية موجود بالفعل');
            }
            
            // إعداد البيانات
            $employeeData = [
                'employee_number' => $data['employee_number'],
                'first_name_ar' => $data['first_name_ar'],
                'last_name_ar' => $data['last_name_ar'],
                'first_name_fr' => $data['first_name_fr'] ?? '',
                'last_name_fr' => $data['last_name_fr'] ?? '',
                'birth_date' => $data['birth_date'],
                'birth_place_ar' => $data['birth_place_ar'] ?? '',
                'birth_place_fr' => $data['birth_place_fr'] ?? '',
                'gender' => $data['gender'],
                'marital_status' => $data['marital_status'] ?? 'single',
                'children_count' => $data['children_count'] ?? 0,
                'national_id' => $data['national_id'],
                'social_security_number' => $data['social_security_number'] ?? '',
                'address_ar' => $data['address_ar'] ?? '',
                'address_fr' => $data['address_fr'] ?? '',
                'phone' => $data['phone'] ?? '',
                'email' => $data['email'] ?? '',
                'hire_date' => $data['hire_date'],
                'position_id' => $data['position_id'],
                'status' => $data['status'] ?? 'active',
                'bank_account' => $data['bank_account'] ?? '',
                'bank_name' => $data['bank_name'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $employeeId = $this->db->insert('employees', $employeeData);
            
            // تسجيل العملية في السجل
            $this->logAction('create', 'employees', $employeeId, null, $employeeData);
            
            $this->db->commit();
            return $employeeId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * تحديث بيانات موظف
     */
    public function updateEmployee($id, $data) {
        try {
            $this->db->beginTransaction();
            
            // الحصول على البيانات القديمة
            $oldData = $this->getEmployeeById($id);
            if (!$oldData) {
                throw new Exception('الموظف غير موجود');
            }
            
            // التحقق من عدم تكرار رقم التسجيل (إذا تم تغييره)
            if ($data['employee_number'] !== $oldData['employee_number']) {
                if ($this->employeeNumberExists($data['employee_number'])) {
                    throw new Exception('رقم التسجيل موجود بالفعل');
                }
            }
            
            // التحقق من عدم تكرار رقم الهوية (إذا تم تغييره)
            if ($data['national_id'] !== $oldData['national_id']) {
                if ($this->nationalIdExists($data['national_id'])) {
                    throw new Exception('رقم الهوية الوطنية موجود بالفعل');
                }
            }
            
            // إعداد البيانات للتحديث
            $updateData = [
                'employee_number' => $data['employee_number'],
                'first_name_ar' => $data['first_name_ar'],
                'last_name_ar' => $data['last_name_ar'],
                'first_name_fr' => $data['first_name_fr'] ?? '',
                'last_name_fr' => $data['last_name_fr'] ?? '',
                'birth_date' => $data['birth_date'],
                'birth_place_ar' => $data['birth_place_ar'] ?? '',
                'birth_place_fr' => $data['birth_place_fr'] ?? '',
                'gender' => $data['gender'],
                'marital_status' => $data['marital_status'] ?? 'single',
                'children_count' => $data['children_count'] ?? 0,
                'national_id' => $data['national_id'],
                'social_security_number' => $data['social_security_number'] ?? '',
                'address_ar' => $data['address_ar'] ?? '',
                'address_fr' => $data['address_fr'] ?? '',
                'phone' => $data['phone'] ?? '',
                'email' => $data['email'] ?? '',
                'hire_date' => $data['hire_date'],
                'position_id' => $data['position_id'],
                'status' => $data['status'] ?? 'active',
                'bank_account' => $data['bank_account'] ?? '',
                'bank_name' => $data['bank_name'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->update('employees', $updateData, 'id = :id', ['id' => $id]);
            
            // تسجيل العملية في السجل
            $this->logAction('update', 'employees', $id, $oldData, $updateData);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * حذف موظف (حذف منطقي)
     */
    public function deleteEmployee($id) {
        try {
            $this->db->beginTransaction();
            
            $employee = $this->getEmployeeById($id);
            if (!$employee) {
                throw new Exception('الموظف غير موجود');
            }
            
            // التحقق من وجود فيش راتب للموظف
            $payrollCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM payrolls WHERE employee_id = :id",
                ['id' => $id]
            );
            
            if ($payrollCount['count'] > 0) {
                // حذف منطقي فقط
                $this->db->update('employees', 
                    ['status' => 'deleted', 'updated_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $id]
                );
            } else {
                // حذف فعلي
                $this->db->delete('employees', 'id = :id', ['id' => $id]);
            }
            
            // تسجيل العملية في السجل
            $this->logAction('delete', 'employees', $id, $employee, null);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود رقم التسجيل
     */
    private function employeeNumberExists($employeeNumber, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM employees WHERE employee_number = :employee_number AND status != 'deleted'";
        $params = ['employee_number' => $employeeNumber];
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود رقم الهوية الوطنية
     */
    private function nationalIdExists($nationalId, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM employees WHERE national_id = :national_id AND status != 'deleted'";
        $params = ['national_id' => $nationalId];
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات الموظفين
     */
    public function getEmployeeStats() {
        $stats = [];
        
        // إجمالي الموظفين النشطين
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM employees WHERE status = 'active'");
        $stats['active_employees'] = $result['count'];
        
        // الموظفين الجدد هذا الشهر
        $result = $this->db->selectOne("
            SELECT COUNT(*) as count 
            FROM employees 
            WHERE status = 'active' 
            AND YEAR(hire_date) = YEAR(NOW()) 
            AND MONTH(hire_date) = MONTH(NOW())
        ");
        $stats['new_employees_this_month'] = $result['count'];
        
        // توزيع الموظفين حسب الأقسام
        $stats['by_department'] = $this->db->select("
            SELECT 
                d.name_ar as department_name,
                COUNT(e.id) as employee_count
            FROM departments d
            LEFT JOIN positions p ON d.id = p.department_id
            LEFT JOIN employees e ON p.id = e.position_id AND e.status = 'active'
            GROUP BY d.id, d.name_ar
            ORDER BY employee_count DESC
        ");
        
        // توزيع الموظفين حسب الجنس
        $stats['by_gender'] = $this->db->select("
            SELECT 
                gender,
                COUNT(*) as count
            FROM employees 
            WHERE status = 'active'
            GROUP BY gender
        ");
        
        return $stats;
    }
    
    /**
     * البحث في الموظفين
     */
    public function searchEmployees($searchTerm, $limit = 10) {
        $query = "
            SELECT 
                e.id,
                e.employee_number,
                e.first_name_ar,
                e.last_name_ar,
                e.first_name_fr,
                e.last_name_fr,
                p.title_ar as position_title_ar,
                p.title_fr as position_title_fr,
                d.name_ar as department_name_ar
            FROM employees e
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE e.status = 'active'
            AND (
                e.first_name_ar LIKE :search 
                OR e.last_name_ar LIKE :search 
                OR e.first_name_fr LIKE :search 
                OR e.last_name_fr LIKE :search 
                OR e.employee_number LIKE :search
                OR e.national_id LIKE :search
            )
            ORDER BY e.first_name_ar, e.last_name_ar
            LIMIT :limit
        ";
        
        return $this->db->select($query, [
            'search' => '%' . $searchTerm . '%',
            'limit' => $limit
        ]);
    }
    
    /**
     * تسجيل العملية في السجل
     */
    private function logAction($action, $table, $recordId, $oldValues, $newValues) {
        if (isset($_SESSION['user_id'])) {
            $this->db->insert('audit_logs', [
                'user_id' => $_SESSION['user_id'],
                'action' => $action,
                'table_name' => $table,
                'record_id' => $recordId,
                'old_values' => $oldValues ? json_encode($oldValues) : null,
                'new_values' => $newValues ? json_encode($newValues) : null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * تصدير بيانات الموظفين
     */
    public function exportEmployees($format = 'csv', $filters = []) {
        $employees = $this->getAllEmployees($filters);
        
        switch ($format) {
            case 'csv':
                return $this->exportToCSV($employees);
            case 'excel':
                return $this->exportToExcel($employees);
            default:
                throw new Exception('تنسيق التصدير غير مدعوم');
        }
    }
    
    /**
     * تصدير إلى CSV
     */
    private function exportToCSV($employees) {
        $filename = 'employees_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = TEMP_PATH . $filename;

        // إنشاء مجلد temp إذا لم يكن موجوداً
        if (!is_dir(TEMP_PATH)) {
            mkdir(TEMP_PATH, 0755, true);
        }

        $file = fopen($filepath, 'w');

        // كتابة العناوين
        fputcsv($file, [
            'رقم التسجيل',
            'الاسم الكامل',
            'المنصب',
            'القسم',
            'تاريخ التوظيف',
            'الحالة',
            'الهاتف',
            'البريد الإلكتروني'
        ]);

        // كتابة البيانات
        foreach ($employees as $employee) {
            fputcsv($file, [
                $employee['employee_number'],
                $employee['first_name_ar'] . ' ' . $employee['last_name_ar'],
                $employee['position_title_ar'],
                $employee['department_name_ar'],
                $employee['hire_date'],
                $employee['status'],
                $employee['phone'],
                $employee['email']
            ]);
        }

        fclose($file);
        return $filepath;
    }
}
}
?>
