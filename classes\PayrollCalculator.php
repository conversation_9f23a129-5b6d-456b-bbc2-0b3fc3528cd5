<?php
/**
 * فئة حساب الرواتب
 * Payroll Calculator Class
 */

class PayrollCalculator {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * حساب راتب موظف لفترة معينة
     */
    public function calculateEmployeePayroll($employeeId, $payrollPeriodId, $overrides = []) {
        try {
            // الحصول على بيانات الموظف
            $employee = $this->getEmployeeData($employeeId);
            if (!$employee) {
                throw new Exception('الموظف غير موجود');
            }
            
            // الحصول على فترة الراتب
            $period = $this->getPayrollPeriod($payrollPeriodId);
            if (!$period) {
                throw new Exception('فترة الراتب غير موجودة');
            }
            
            // حساب الراتب الأساسي
            $baseSalary = $this->calculateBaseSalary($employee, $period, $overrides);
            
            // حساب المنح والتعويضات
            $allowances = $this->calculateAllowances($employee, $period, $overrides);
            
            // حساب الراتب الإجمالي
            $grossSalary = $baseSalary + array_sum(array_column($allowances, 'amount'));
            
            // حساب أساس الضمان الاجتماعي
            $socialSecurityBase = $this->calculateSocialSecurityBase($grossSalary);
            
            // حساب الاستقطاعات
            $deductions = $this->calculateDeductions($employee, $grossSalary, $socialSecurityBase, $overrides);
            
            // حساب الراتب الصافي
            $totalDeductions = array_sum(array_column($deductions, 'amount'));
            $netSalary = $grossSalary - $totalDeductions;
            
            return [
                'employee_id' => $employeeId,
                'payroll_period_id' => $payrollPeriodId,
                'base_salary' => $baseSalary,
                'gross_salary' => $grossSalary,
                'social_security_base' => $socialSecurityBase,
                'taxable_income' => $this->calculateTaxableIncome($grossSalary, $allowances),
                'total_allowances' => array_sum(array_column($allowances, 'amount')),
                'total_deductions' => $totalDeductions,
                'net_salary' => $netSalary,
                'days_worked' => $overrides['days_worked'] ?? $period['working_days'],
                'hours_worked' => $overrides['hours_worked'] ?? $period['working_hours'],
                'overtime_hours' => $overrides['overtime_hours'] ?? 0,
                'absence_days' => $overrides['absence_days'] ?? 0,
                'allowances' => $allowances,
                'deductions' => $deductions,
                'status' => 'calculated'
            ];
            
        } catch (Exception $e) {
            throw new Exception('خطأ في حساب الراتب: ' . $e->getMessage());
        }
    }
    
    /**
     * حساب الراتب الأساسي
     */
    private function calculateBaseSalary($employee, $period, $overrides) {
        $baseSalary = $employee['base_salary'];
        $daysWorked = $overrides['days_worked'] ?? $period['working_days'];
        $absenceDays = $overrides['absence_days'] ?? 0;
        
        // خصم أيام الغياب
        if ($absenceDays > 0) {
            $dailyRate = $baseSalary / $period['working_days'];
            $baseSalary -= ($dailyRate * $absenceDays);
        }
        
        return round($baseSalary, 2);
    }
    
    /**
     * حساب المنح والتعويضات
     */
    private function calculateAllowances($employee, $period, $overrides) {
        $allowances = [];
        
        // الحصول على أنواع المنح المطبقة على الموظف
        $allowanceTypes = $this->db->select("
            SELECT * FROM allowance_types 
            WHERE is_active = 1 
            ORDER BY name_ar
        ");
        
        foreach ($allowanceTypes as $type) {
            $amount = 0;
            
            // التحقق من وجود تخصيص مخصص
            if (isset($overrides['allowances'][$type['id']])) {
                $amount = $overrides['allowances'][$type['id']];
            } else {
                // حساب المنحة حسب النوع
                switch ($type['calculation_type']) {
                    case 'fixed':
                        $amount = $type['default_amount'];
                        break;
                        
                    case 'percentage':
                        $amount = $employee['base_salary'] * $type['default_amount'];
                        break;
                        
                    case 'days':
                        $amount = $type['default_amount'] * ($overrides['days_worked'] ?? $period['working_days']);
                        break;
                        
                    case 'hours':
                        $amount = $type['default_amount'] * ($overrides['hours_worked'] ?? $period['working_hours']);
                        break;
                }
            }
            
            // إضافة المنحة إذا كان لها قيمة
            if ($amount > 0) {
                $allowances[] = [
                    'allowance_type_id' => $type['id'],
                    'name_ar' => $type['name_ar'],
                    'name_fr' => $type['name_fr'],
                    'amount' => round($amount, 2),
                    'calculation_base' => $employee['base_salary'],
                    'rate' => $type['default_amount'],
                    'quantity' => 1,
                    'is_taxable' => $type['is_taxable'],
                    'is_social_security_subject' => $type['is_social_security_subject']
                ];
            }
        }
        
        // إضافة الساعات الإضافية
        if (isset($overrides['overtime_hours']) && $overrides['overtime_hours'] > 0) {
            $hourlyRate = $employee['base_salary'] / $period['working_hours'];
            $overtimeRate = $hourlyRate * 1.5; // 50% زيادة
            $overtimeAmount = $overtimeRate * $overrides['overtime_hours'];
            
            $allowances[] = [
                'allowance_type_id' => null,
                'name_ar' => 'ساعات إضافية',
                'name_fr' => 'Heures supplémentaires',
                'amount' => round($overtimeAmount, 2),
                'calculation_base' => $hourlyRate,
                'rate' => 1.5,
                'quantity' => $overrides['overtime_hours'],
                'is_taxable' => true,
                'is_social_security_subject' => true
            ];
        }
        
        return $allowances;
    }
    
    /**
     * حساب الاستقطاعات
     */
    private function calculateDeductions($employee, $grossSalary, $socialSecurityBase, $overrides) {
        $deductions = [];
        
        // اشتراكات الضمان الاجتماعي
        $socialSecurityContributions = calculateSocialSecurityContributions($socialSecurityBase);
        
        foreach ($socialSecurityContributions['employee'] as $type => $amount) {
            $deductionNames = [
                'social_security' => ['ar' => 'اشتراك الضمان الاجتماعي', 'fr' => 'Cotisation Sécurité Sociale'],
                'unemployment' => ['ar' => 'اشتراك التأمين على البطالة', 'fr' => 'Cotisation Assurance Chômage'],
                'early_retirement' => ['ar' => 'اشتراك التقاعد المسبق', 'fr' => 'Cotisation Retraite Anticipée']
            ];
            
            $deductions[] = [
                'deduction_type_id' => null,
                'name_ar' => $deductionNames[$type]['ar'],
                'name_fr' => $deductionNames[$type]['fr'],
                'amount' => $amount,
                'calculation_base' => $socialSecurityBase,
                'rate' => SOCIAL_SECURITY_RATES['employee'][$type],
                'category' => 'social_security'
            ];
        }
        
        // ضريبة الدخل الإجمالي
        $taxableIncome = $this->calculateTaxableIncome($grossSalary, []);
        $taxExemptions = calculateTaxExemptions($employee['marital_status'], $employee['children_count']);
        $irgTax = calculateIRG($taxableIncome, $taxExemptions);
        
        if ($irgTax > 0) {
            $deductions[] = [
                'deduction_type_id' => null,
                'name_ar' => 'ضريبة الدخل الإجمالي',
                'name_fr' => 'Impôt sur le Revenu Global',
                'amount' => $irgTax,
                'calculation_base' => $taxableIncome - $taxExemptions,
                'rate' => null,
                'category' => 'tax'
            ];
        }
        
        // استقطاعات أخرى مخصصة
        if (isset($overrides['deductions'])) {
            foreach ($overrides['deductions'] as $deduction) {
                $deductions[] = $deduction;
            }
        }
        
        return $deductions;
    }
    
    /**
     * حساب أساس الضمان الاجتماعي
     */
    private function calculateSocialSecurityBase($grossSalary) {
        return min($grossSalary, MAX_SOCIAL_SECURITY_BASE);
    }
    
    /**
     * حساب الدخل الخاضع للضريبة
     */
    private function calculateTaxableIncome($grossSalary, $allowances) {
        $taxableIncome = $grossSalary;
        
        // خصم المنح غير الخاضعة للضريبة
        foreach ($allowances as $allowance) {
            if (!$allowance['is_taxable']) {
                $taxableIncome -= $allowance['amount'];
            }
        }
        
        return max(0, $taxableIncome);
    }
    
    /**
     * الحصول على بيانات الموظف
     */
    private function getEmployeeData($employeeId) {
        return $this->db->selectOne("
            SELECT 
                e.*,
                p.base_salary,
                p.title_ar as position_title_ar,
                p.title_fr as position_title_fr,
                d.name_ar as department_name_ar,
                d.name_fr as department_name_fr
            FROM employees e
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE e.id = :id AND e.status = 'active'
        ", ['id' => $employeeId]);
    }
    
    /**
     * الحصول على فترة الراتب
     */
    private function getPayrollPeriod($payrollPeriodId) {
        return $this->db->selectOne("
            SELECT * FROM payroll_periods 
            WHERE id = :id
        ", ['id' => $payrollPeriodId]);
    }
    
    /**
     * حفظ حساب الراتب
     */
    public function savePayroll($payrollData) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من عدم وجود راتب محسوب للموظف في نفس الفترة
            $existing = $this->db->selectOne("
                SELECT id FROM payrolls 
                WHERE employee_id = :employee_id 
                AND payroll_period_id = :payroll_period_id
            ", [
                'employee_id' => $payrollData['employee_id'],
                'payroll_period_id' => $payrollData['payroll_period_id']
            ]);
            
            if ($existing) {
                // تحديث الراتب الموجود
                $payrollId = $existing['id'];
                $this->updatePayroll($payrollId, $payrollData);
            } else {
                // إنشاء راتب جديد
                $payrollId = $this->createPayroll($payrollData);
            }
            
            $this->db->commit();
            return $payrollId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء راتب جديد
     */
    private function createPayroll($payrollData) {
        // إدراج الراتب الرئيسي
        $payrollId = $this->db->insert('payrolls', [
            'employee_id' => $payrollData['employee_id'],
            'payroll_period_id' => $payrollData['payroll_period_id'],
            'base_salary' => $payrollData['base_salary'],
            'gross_salary' => $payrollData['gross_salary'],
            'taxable_income' => $payrollData['taxable_income'],
            'social_security_base' => $payrollData['social_security_base'],
            'total_allowances' => $payrollData['total_allowances'],
            'total_deductions' => $payrollData['total_deductions'],
            'net_salary' => $payrollData['net_salary'],
            'days_worked' => $payrollData['days_worked'],
            'hours_worked' => $payrollData['hours_worked'],
            'overtime_hours' => $payrollData['overtime_hours'],
            'absence_days' => $payrollData['absence_days'],
            'status' => $payrollData['status'],
            'calculated_at' => date('Y-m-d H:i:s')
        ]);
        
        // إدراج المنح
        foreach ($payrollData['allowances'] as $allowance) {
            $this->db->insert('payroll_allowances', [
                'payroll_id' => $payrollId,
                'allowance_type_id' => $allowance['allowance_type_id'],
                'amount' => $allowance['amount'],
                'calculation_base' => $allowance['calculation_base'],
                'rate' => $allowance['rate'],
                'quantity' => $allowance['quantity']
            ]);
        }
        
        // إدراج الاستقطاعات
        foreach ($payrollData['deductions'] as $deduction) {
            $this->db->insert('payroll_deductions', [
                'payroll_id' => $payrollId,
                'deduction_type_id' => $deduction['deduction_type_id'],
                'amount' => $deduction['amount'],
                'calculation_base' => $deduction['calculation_base'],
                'rate' => $deduction['rate']
            ]);
        }
        
        return $payrollId;
    }
    
    /**
     * تحديث راتب موجود
     */
    private function updatePayroll($payrollId, $payrollData) {
        // تحديث الراتب الرئيسي
        $this->db->update('payrolls', [
            'base_salary' => $payrollData['base_salary'],
            'gross_salary' => $payrollData['gross_salary'],
            'taxable_income' => $payrollData['taxable_income'],
            'social_security_base' => $payrollData['social_security_base'],
            'total_allowances' => $payrollData['total_allowances'],
            'total_deductions' => $payrollData['total_deductions'],
            'net_salary' => $payrollData['net_salary'],
            'days_worked' => $payrollData['days_worked'],
            'hours_worked' => $payrollData['hours_worked'],
            'overtime_hours' => $payrollData['overtime_hours'],
            'absence_days' => $payrollData['absence_days'],
            'status' => $payrollData['status'],
            'calculated_at' => date('Y-m-d H:i:s')
        ], 'id = :id', ['id' => $payrollId]);
        
        // حذف المنح والاستقطاعات القديمة
        $this->db->delete('payroll_allowances', 'payroll_id = :id', ['id' => $payrollId]);
        $this->db->delete('payroll_deductions', 'payroll_id = :id', ['id' => $payrollId]);
        
        // إدراج المنح الجديدة
        foreach ($payrollData['allowances'] as $allowance) {
            $this->db->insert('payroll_allowances', [
                'payroll_id' => $payrollId,
                'allowance_type_id' => $allowance['allowance_type_id'],
                'amount' => $allowance['amount'],
                'calculation_base' => $allowance['calculation_base'],
                'rate' => $allowance['rate'],
                'quantity' => $allowance['quantity']
            ]);
        }
        
        // إدراج الاستقطاعات الجديدة
        foreach ($payrollData['deductions'] as $deduction) {
            $this->db->insert('payroll_deductions', [
                'payroll_id' => $payrollId,
                'deduction_type_id' => $deduction['deduction_type_id'],
                'amount' => $deduction['amount'],
                'calculation_base' => $deduction['calculation_base'],
                'rate' => $deduction['rate']
            ]);
        }
    }
    
    /**
     * حساب رواتب جميع الموظفين لفترة معينة
     */
    public function calculateAllPayrolls($payrollPeriodId, $employeeIds = null) {
        $results = [];
        
        // الحصول على قائمة الموظفين
        $whereClause = "e.status = 'active'";
        $params = [];
        
        if ($employeeIds) {
            $placeholders = str_repeat('?,', count($employeeIds) - 1) . '?';
            $whereClause .= " AND e.id IN ($placeholders)";
            $params = $employeeIds;
        }
        
        $employees = $this->db->select("
            SELECT e.id FROM employees e 
            WHERE $whereClause
            ORDER BY e.first_name_ar, e.last_name_ar
        ", $params);
        
        foreach ($employees as $employee) {
            try {
                $payrollData = $this->calculateEmployeePayroll($employee['id'], $payrollPeriodId);
                $payrollId = $this->savePayroll($payrollData);
                
                $results[] = [
                    'employee_id' => $employee['id'],
                    'payroll_id' => $payrollId,
                    'status' => 'success',
                    'net_salary' => $payrollData['net_salary']
                ];
            } catch (Exception $e) {
                $results[] = [
                    'employee_id' => $employee['id'],
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
}
?>
