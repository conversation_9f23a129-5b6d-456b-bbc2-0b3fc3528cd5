-- بيانات أولية لنظام إدارة فيش الراتب الجزائري
-- Initial Data for Algerian Payroll Management System

-- إدراج أنواع المنح والتعويضات الأساسية
INSERT INTO allowance_types (name_ar, name_fr, code, is_taxable, is_social_security_subject, calculation_type, default_amount, description_ar, description_fr) VALUES
('منحة المنطقة', 'Indemnité de Zone', 'ZONE', TRUE, TRUE, 'fixed', 2000.00, 'منحة المنطقة الجغرافية', 'Indemnité géographique'),
('منحة الخبرة', 'Prime d\'Expérience', 'EXP', TRUE, TRUE, 'percentage', 0.00, 'منحة الخبرة المهنية', 'Prime d\'expérience professionnelle'),
('منحة المسؤولية', 'Prime de Responsabilité', 'RESP', TRUE, TRUE, 'fixed', 0.00, 'منحة المسؤولية الإدارية', 'Prime de responsabilité administrative'),
('منحة المردودية', 'Prime de Rendement', 'REND', TRUE, TRUE, 'percentage', 0.00, 'منحة المردودية والأداء', 'Prime de rendement et performance'),
('تعويض النقل', 'Indemnité de Transport', 'TRANS', FALSE, FALSE, 'fixed', 3000.00, 'تعويض وسائل النقل', 'Indemnité de transport'),
('تعويض الوجبات', 'Indemnité de Repas', 'MEAL', FALSE, FALSE, 'fixed', 2800.00, 'تعويض الوجبات', 'Indemnité de restauration'),
('منحة الأطفال', 'Allocations Familiales', 'CHILD', FALSE, FALSE, 'fixed', 600.00, 'منحة الأطفال', 'Allocations familiales'),
('ساعات إضافية', 'Heures Supplémentaires', 'OT', TRUE, TRUE, 'hours', 0.00, 'الساعات الإضافية', 'Heures supplémentaires'),
('منحة الليل', 'Prime de Nuit', 'NIGHT', TRUE, TRUE, 'percentage', 0.15, 'منحة العمل الليلي', 'Prime de travail de nuit'),
('منحة المخاطر', 'Prime de Risque', 'RISK', TRUE, TRUE, 'percentage', 0.10, 'منحة المخاطر المهنية', 'Prime de risques professionnels');

-- إدراج أنواع الاستقطاعات الأساسية
INSERT INTO deduction_types (name_ar, name_fr, code, category, calculation_type, rate, max_amount, min_amount, is_employer_contribution, description_ar, description_fr) VALUES
-- اشتراكات الضمان الاجتماعي
('اشتراك الضمان الاجتماعي', 'Cotisation Sécurité Sociale', 'SS', 'social_security', 'percentage', 0.0900, NULL, NULL, FALSE, 'اشتراك الضمان الاجتماعي 9%', 'Cotisation sécurité sociale 9%'),
('اشتراك التأمين على البطالة', 'Cotisation Assurance Chômage', 'UNEMP', 'social_security', 'percentage', 0.0050, NULL, NULL, FALSE, 'اشتراك التأمين على البطالة 0.5%', 'Cotisation assurance chômage 0.5%'),
('اشتراك التقاعد المسبق', 'Cotisation Retraite Anticipée', 'EARLY_RET', 'social_security', 'percentage', 0.0025, NULL, NULL, FALSE, 'اشتراك التقاعد المسبق 0.25%', 'Cotisation retraite anticipée 0.25%'),

-- مساهمات صاحب العمل
('مساهمة صاحب العمل - ضمان اجتماعي', 'Contribution Employeur SS', 'EMP_SS', 'social_security', 'percentage', 0.2600, NULL, NULL, TRUE, 'مساهمة صاحب العمل في الضمان الاجتماعي 26%', 'Contribution employeur sécurité sociale 26%'),
('مساهمة صاحب العمل - تأمين بطالة', 'Contribution Employeur Chômage', 'EMP_UNEMP', 'social_security', 'percentage', 0.0150, NULL, NULL, TRUE, 'مساهمة صاحب العمل في التأمين على البطالة 1.5%', 'Contribution employeur assurance chômage 1.5%'),
('مساهمة صاحب العمل - تقاعد مسبق', 'Contribution Employeur Retraite', 'EMP_RET', 'social_security', 'percentage', 0.0025, NULL, NULL, TRUE, 'مساهمة صاحب العمل في التقاعد المسبق 0.25%', 'Contribution employeur retraite anticipée 0.25%'),
('مساهمة صاحب العمل - حوادث عمل', 'Contribution Accidents Travail', 'EMP_ACC', 'social_security', 'percentage', 0.0125, NULL, NULL, TRUE, 'مساهمة صاحب العمل في حوادث العمل 1.25%', 'Contribution accidents du travail 1.25%'),

-- الضرائب
('ضريبة الدخل الإجمالي', 'Impôt sur le Revenu Global', 'IRG', 'tax', 'percentage', 0.0000, NULL, NULL, FALSE, 'ضريبة الدخل الإجمالي - معدل متغير', 'Impôt sur le revenu global - taux variable'),

-- استقطاعات أخرى
('قرض السكن', 'Prêt Logement', 'LOAN_HOUSE', 'other', 'fixed', 0.0000, NULL, NULL, FALSE, 'استقطاع قرض السكن', 'Retenue prêt logement'),
('قرض الاستهلاك', 'Prêt Consommation', 'LOAN_CONS', 'other', 'fixed', 0.0000, NULL, NULL, FALSE, 'استقطاع قرض الاستهلاك', 'Retenue prêt consommation'),
('تأمين جماعي', 'Assurance Groupe', 'GROUP_INS', 'other', 'fixed', 0.0000, NULL, NULL, FALSE, 'استقطاع التأمين الجماعي', 'Retenue assurance groupe');

-- إدراج شركة تجريبية
INSERT INTO companies (name_ar, name_fr, address_ar, address_fr, commercial_register, tax_id, cnas_number, cnr_number, phone, email) VALUES
('المؤسسة العمومية للأشغال الكبرى', 'EPE GRANDS TRAVAUX OUEST', 'الطريق الوطني رقم 04، وهران', 'Route Nationale N°04, Oran', '31/00-0985674-B-23', '003131098567412', '31056789234', '31056789234', '+213-41-123456', '<EMAIL>');

-- إدراج أقسام تجريبية
INSERT INTO departments (company_id, name_ar, name_fr, description_ar, description_fr) VALUES
(1, 'الإدارة العامة', 'Administration Générale', 'الإدارة العامة للمؤسسة', 'Administration générale de l\'entreprise'),
(1, 'الهندسة والمشاريع', 'Ingénierie et Projets', 'قسم الهندسة والمشاريع', 'Département ingénierie et projets'),
(1, 'الموارد البشرية', 'Ressources Humaines', 'قسم الموارد البشرية', 'Département des ressources humaines'),
(1, 'المالية والمحاسبة', 'Finance et Comptabilité', 'قسم المالية والمحاسبة', 'Département finance et comptabilité'),
(1, 'العمليات والإنتاج', 'Opérations et Production', 'قسم العمليات والإنتاج', 'Département opérations et production');

-- إدراج مناصب تجريبية
INSERT INTO positions (department_id, title_ar, title_fr, grade_level, base_salary, description_ar, description_fr) VALUES
(1, 'مدير عام', 'Directeur Général', 1, 150000.00, 'المدير العام للمؤسسة', 'Directeur général de l\'entreprise'),
(1, 'مدير إداري', 'Directeur Administratif', 2, 120000.00, 'مدير الشؤون الإدارية', 'Directeur des affaires administratives'),
(2, 'مهندس مشاريع أول', 'Ingénieur Projets Senior', 3, 85000.00, 'مهندس مشاريع أول', 'Ingénieur projets senior'),
(2, 'مهندس مشاريع', 'Ingénieur Projets', 4, 65000.00, 'مهندس مشاريع', 'Ingénieur projets'),
(2, 'تقني سامي', 'Technicien Supérieur', 5, 45000.00, 'تقني سامي', 'Technicien supérieur'),
(3, 'مدير الموارد البشرية', 'Directeur RH', 2, 110000.00, 'مدير الموارد البشرية', 'Directeur des ressources humaines'),
(3, 'مسؤول الموارد البشرية', 'Responsable RH', 4, 70000.00, 'مسؤول الموارد البشرية', 'Responsable ressources humaines'),
(4, 'مدير مالي', 'Directeur Financier', 2, 115000.00, 'المدير المالي', 'Directeur financier'),
(4, 'محاسب رئيسي', 'Comptable Principal', 4, 60000.00, 'محاسب رئيسي', 'Comptable principal'),
(4, 'محاسب', 'Comptable', 5, 45000.00, 'محاسب', 'Comptable'),
(5, 'مدير العمليات', 'Directeur Opérations', 2, 105000.00, 'مدير العمليات', 'Directeur des opérations'),
(5, 'مشرف إنتاج', 'Superviseur Production', 4, 55000.00, 'مشرف الإنتاج', 'Superviseur de production'),
(5, 'عامل مختص', 'Ouvrier Spécialisé', 6, 35000.00, 'عامل مختص', 'Ouvrier spécialisé');

-- إدراج موظف تجريبي
INSERT INTO employees (employee_number, first_name_ar, last_name_ar, first_name_fr, last_name_fr, birth_date, birth_place_ar, birth_place_fr, gender, marital_status, children_count, national_id, social_security_number, address_ar, address_fr, phone, email, hire_date, position_id, bank_account, bank_name) VALUES
('2024/EMP/0156', 'محمد الأمين', 'بوعلام', 'Mohamed Amine', 'BOUALAM', '1985-03-15', 'وهران', 'Oran', 'M', 'married', 2, '*************', '*************789', 'حي السلام، وهران', 'Cité Es-Salam, Oran', '+213-**********', '<EMAIL>', '2020-01-12', 4, '**************', 'البنك الوطني الجزائري');

-- إدراج مستخدم إداري
INSERT INTO users (username, email, password_hash, full_name_ar, full_name_fr, role, company_id, employee_id) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'Administrateur', 'admin', 1, 1),
('hr_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير الموارد البشرية', 'Directeur RH', 'hr_manager', 1, NULL),
('accountant', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المحاسب الرئيسي', 'Comptable Principal', 'accountant', 1, NULL);

-- إدراج فترة راتب تجريبية
INSERT INTO payroll_periods (company_id, year, month, start_date, end_date, working_days, working_hours, status) VALUES
(1, 2024, 11, '2024-11-01', '2024-11-30', 22, 176, 'draft');

-- تحديث مرجع المدير في الأقسام
UPDATE departments SET manager_id = 1 WHERE id = 1;
UPDATE departments SET manager_id = 2 WHERE id = 2;
UPDATE departments SET manager_id = 6 WHERE id = 3;
UPDATE departments SET manager_id = 8 WHERE id = 4;
UPDATE departments SET manager_id = 11 WHERE id = 5;
