<?php
/**
 * شريط التنقل المشترك
 * Shared Navigation Bar
 */

// التأكد من وجود الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$lang = $_SESSION['language'] ?? DEFAULT_LANGUAGE;
$user = $_SESSION['user'] ?? null;
$isLoggedIn = isset($_SESSION['user_id']);

// تحديد المسار الحالي لتمييز القائمة النشطة
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo $isLoggedIn ? '../dashboard.php' : '../index.php'; ?>">
            <i class="fas fa-file-invoice-dollar me-2"></i>
            <?php echo $lang === 'ar' ? 'نظام الرواتب' : 'Système de Paie'; ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <?php if ($isLoggedIn): ?>
            <ul class="navbar-nav me-auto">
                <!-- لوحة التحكم -->
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage === 'dashboard.php') ? 'active' : ''; ?>" 
                       href="../dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        <?php echo $lang === 'ar' ? 'لوحة التحكم' : 'Tableau de bord'; ?>
                    </a>
                </li>
                
                <!-- الموظفين -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo ($currentDir === 'employees') ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-users me-1"></i>
                        <?php echo $lang === 'ar' ? 'الموظفين' : 'Employés'; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item <?php echo ($currentPage === 'list.php' && $currentDir === 'employees') ? 'active' : ''; ?>" 
                               href="../employees/list.php">
                                <i class="fas fa-list me-2"></i>
                                <?php echo $lang === 'ar' ? 'قائمة الموظفين' : 'Liste des employés'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item <?php echo ($currentPage === 'add.php' && $currentDir === 'employees') ? 'active' : ''; ?>" 
                               href="../employees/add.php">
                                <i class="fas fa-user-plus me-2"></i>
                                <?php echo $lang === 'ar' ? 'إضافة موظف' : 'Ajouter employé'; ?>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../departments/list.php">
                                <i class="fas fa-building me-2"></i>
                                <?php echo $lang === 'ar' ? 'الأقسام' : 'Départements'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../positions/list.php">
                                <i class="fas fa-briefcase me-2"></i>
                                <?php echo $lang === 'ar' ? 'المناصب' : 'Postes'; ?>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- الرواتب -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo ($currentDir === 'payroll') ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-money-bill-wave me-1"></i>
                        <?php echo $lang === 'ar' ? 'الرواتب' : 'Salaires'; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="../payroll/list.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                <?php echo $lang === 'ar' ? 'فيش الراتب' : 'Bulletins de paie'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../payroll/calculate.php">
                                <i class="fas fa-calculator me-2"></i>
                                <?php echo $lang === 'ar' ? 'حساب الرواتب' : 'Calculer salaires'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../payroll/periods.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <?php echo $lang === 'ar' ? 'فترات الراتب' : 'Périodes de paie'; ?>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../allowances/list.php">
                                <i class="fas fa-plus-circle me-2"></i>
                                <?php echo $lang === 'ar' ? 'المنح والتعويضات' : 'Indemnités'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../deductions/list.php">
                                <i class="fas fa-minus-circle me-2"></i>
                                <?php echo $lang === 'ar' ? 'الاستقطاعات' : 'Déductions'; ?>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- التقارير -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo ($currentDir === 'reports') ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-chart-bar me-1"></i>
                        <?php echo $lang === 'ar' ? 'التقارير' : 'Rapports'; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="../reports/monthly.php">
                                <i class="fas fa-calendar-month me-2"></i>
                                <?php echo $lang === 'ar' ? 'التقرير الشهري' : 'Rapport mensuel'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../reports/annual.php">
                                <i class="fas fa-calendar-year me-2"></i>
                                <?php echo $lang === 'ar' ? 'التقرير السنوي' : 'Rapport annuel'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../reports/social-security.php">
                                <i class="fas fa-shield-alt me-2"></i>
                                <?php echo $lang === 'ar' ? 'تقرير الضمان الاجتماعي' : 'Rapport sécurité sociale'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../reports/taxes.php">
                                <i class="fas fa-receipt me-2"></i>
                                <?php echo $lang === 'ar' ? 'تقرير الضرائب' : 'Rapport fiscal'; ?>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../reports/statistics.php">
                                <i class="fas fa-chart-pie me-2"></i>
                                <?php echo $lang === 'ar' ? 'الإحصائيات' : 'Statistiques'; ?>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- الإعدادات (للمدراء فقط) -->
                <?php if (in_array($_SESSION['role'] ?? '', ['admin', 'hr_manager'])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo ($currentDir === 'settings' || $currentDir === 'companies') ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>
                        <?php echo $lang === 'ar' ? 'الإعدادات' : 'Paramètres'; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="../companies/edit.php">
                                <i class="fas fa-building me-2"></i>
                                <?php echo $lang === 'ar' ? 'بيانات الشركة' : 'Données entreprise'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../users/list.php">
                                <i class="fas fa-users-cog me-2"></i>
                                <?php echo $lang === 'ar' ? 'إدارة المستخدمين' : 'Gestion utilisateurs'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../settings/system.php">
                                <i class="fas fa-sliders-h me-2"></i>
                                <?php echo $lang === 'ar' ? 'إعدادات النظام' : 'Paramètres système'; ?>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../backup/index.php">
                                <i class="fas fa-database me-2"></i>
                                <?php echo $lang === 'ar' ? 'النسخ الاحتياطي' : 'Sauvegarde'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../logs/audit.php">
                                <i class="fas fa-history me-2"></i>
                                <?php echo $lang === 'ar' ? 'سجل العمليات' : 'Journal d\'audit'; ?>
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            <?php endif; ?>
            
            <ul class="navbar-nav">
                <!-- تبديل اللغة -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo $lang === 'ar' ? 'العربية' : 'Français'; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item <?php echo $lang === 'ar' ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, ['lang' => 'ar'])); ?>">
                                🇩🇿 العربية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item <?php echo $lang === 'fr' ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, ['lang' => 'fr'])); ?>">
                                🇫🇷 Français
                            </a>
                        </li>
                    </ul>
                </li>
                
                <?php if ($isLoggedIn): ?>
                <!-- الإشعارات -->
                <li class="nav-item dropdown">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            0
                            <span class="visually-hidden">unread messages</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <li class="dropdown-header">
                            <?php echo $lang === 'ar' ? 'الإشعارات' : 'Notifications'; ?>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item-text text-center text-muted">
                            <?php echo $lang === 'ar' ? 'لا توجد إشعارات جديدة' : 'Aucune nouvelle notification'; ?>
                        </li>
                    </ul>
                </li>
                
                <!-- ملف المستخدم -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="avatar-sm bg-light text-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <?php echo strtoupper(substr($user['full_name_ar'] ?? 'U', 0, 1)); ?>
                        </div>
                        <span class="d-none d-md-inline">
                            <?php echo htmlspecialchars($lang === 'ar' ? $user['full_name_ar'] : $user['full_name_fr']); ?>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="dropdown-header">
                            <div class="text-center">
                                <div class="fw-bold">
                                    <?php echo htmlspecialchars($lang === 'ar' ? $user['full_name_ar'] : $user['full_name_fr']); ?>
                                </div>
                                <small class="text-muted">
                                    <?php 
                                    $roles = [
                                        'admin' => $lang === 'ar' ? 'مدير النظام' : 'Administrateur',
                                        'hr_manager' => $lang === 'ar' ? 'مدير الموارد البشرية' : 'Directeur RH',
                                        'accountant' => $lang === 'ar' ? 'محاسب' : 'Comptable',
                                        'employee' => $lang === 'ar' ? 'موظف' : 'Employé'
                                    ];
                                    echo $roles[$user['role']] ?? $user['role'];
                                    ?>
                                </small>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../profile/index.php">
                                <i class="fas fa-user-edit me-2"></i>
                                <?php echo $lang === 'ar' ? 'الملف الشخصي' : 'Profil'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../profile/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                <?php echo $lang === 'ar' ? 'الإعدادات الشخصية' : 'Paramètres personnels'; ?>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../profile/change-password.php">
                                <i class="fas fa-key me-2"></i>
                                <?php echo $lang === 'ar' ? 'تغيير كلمة المرور' : 'Changer mot de passe'; ?>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <?php echo $lang === 'ar' ? 'تسجيل الخروج' : 'Déconnexion'; ?>
                            </a>
                        </li>
                    </ul>
                </li>
                <?php else: ?>
                <!-- تسجيل الدخول -->
                <li class="nav-item">
                    <a class="nav-link" href="../login.php">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Connexion'; ?>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
    font-weight: 600;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item.active {
    background-color: var(--bs-primary);
    color: white;
}

.dropdown-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.badge {
    font-size: 0.6rem;
}

@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(44, 85, 48, 0.95);
        margin-top: 0.5rem;
        border-radius: 0.5rem;
        padding: 1rem;
    }
}
</style>
