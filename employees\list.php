<?php
/**
 * قائمة الموظفين
 * Employees List
 */

require_once '../config/config.php';
require_once '../classes/Database.php';
require_once '../classes/Employee.php';

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$lang = $_SESSION['language'] ?? DEFAULT_LANGUAGE;
$employee = new Employee();

// معالجة الفلاتر
$filters = [
    'status' => $_GET['status'] ?? '',
    'department_id' => $_GET['department_id'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// الحصول على قائمة الموظفين
try {
    $employees = $employee->getAllEmployees($filters);
    $stats = $employee->getEmployeeStats();
} catch (Exception $e) {
    $error = $lang === 'ar' ? 'حدث خطأ في تحميل البيانات' : 'Erreur lors du chargement des données';
    error_log('Employee list error: ' . $e->getMessage());
}

// الحصول على قائمة الأقسام للفلتر
try {
    $db = Database::getInstance();
    $departments = $db->select("SELECT id, name_ar, name_fr FROM departments ORDER BY name_ar");
} catch (Exception $e) {
    $departments = [];
}

$pageTitle = $lang === 'ar' ? 'قائمة الموظفين' : 'Liste des employés';
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo in_array($lang, RTL_LANGUAGES) ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . ($lang === 'ar' ? APP_NAME : APP_NAME_FR); ?></title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <?php if ($lang === 'ar'): ?>
    <link href="../assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="<?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>">
    
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1"><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="../dashboard.php">
                                        <?php echo $lang === 'ar' ? 'لوحة التحكم' : 'Tableau de bord'; ?>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="add.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $lang === 'ar' ? 'إضافة موظف' : 'Ajouter employé'; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'الموظفين النشطين' : 'Employés actifs'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo $stats['active_employees'] ?? 0; ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'موظفين جدد هذا الشهر' : 'Nouveaux ce mois'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo $stats['new_employees_this_month'] ?? 0; ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-plus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'الأقسام' : 'Départements'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo count($departments); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">
                                    <?php echo $lang === 'ar' ? 'إجمالي الموظفين' : 'Total employés'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo count($employees ?? []); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            <?php echo $lang === 'ar' ? 'البحث والفلترة' : 'Recherche et filtres'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">
                                    <?php echo $lang === 'ar' ? 'البحث' : 'Recherche'; ?>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="<?php echo htmlspecialchars($filters['search']); ?>"
                                       placeholder="<?php echo $lang === 'ar' ? 'اسم الموظف أو رقم التسجيل' : 'Nom employé ou numéro'; ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">
                                    <?php echo $lang === 'ar' ? 'القسم' : 'Département'; ?>
                                </label>
                                <select class="form-select" name="department_id">
                                    <option value="">
                                        <?php echo $lang === 'ar' ? 'جميع الأقسام' : 'Tous les départements'; ?>
                                    </option>
                                    <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo $filters['department_id'] == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo $lang === 'ar' ? $dept['name_ar'] : $dept['name_fr']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">
                                    <?php echo $lang === 'ar' ? 'الحالة' : 'Statut'; ?>
                                </label>
                                <select class="form-select" name="status">
                                    <option value="">
                                        <?php echo $lang === 'ar' ? 'جميع الحالات' : 'Tous les statuts'; ?>
                                    </option>
                                    <option value="active" <?php echo $filters['status'] === 'active' ? 'selected' : ''; ?>>
                                        <?php echo $lang === 'ar' ? 'نشط' : 'Actif'; ?>
                                    </option>
                                    <option value="inactive" <?php echo $filters['status'] === 'inactive' ? 'selected' : ''; ?>>
                                        <?php echo $lang === 'ar' ? 'غير نشط' : 'Inactif'; ?>
                                    </option>
                                    <option value="terminated" <?php echo $filters['status'] === 'terminated' ? 'selected' : ''; ?>>
                                        <?php echo $lang === 'ar' ? 'منتهي الخدمة' : 'Terminé'; ?>
                                    </option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>
                                        <?php echo $lang === 'ar' ? 'بحث' : 'Rechercher'; ?>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employees Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            <?php echo $lang === 'ar' ? 'قائمة الموظفين' : 'Liste des employés'; ?>
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-1"></i>
                                <?php echo $lang === 'ar' ? 'تصدير' : 'Exporter'; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="export.php?format=csv">
                                    <i class="fas fa-file-csv me-2"></i>CSV
                                </a></li>
                                <li><a class="dropdown-item" href="export.php?format=excel">
                                    <i class="fas fa-file-excel me-2"></i>Excel
                                </a></li>
                                <li><a class="dropdown-item" href="export.php?format=pdf">
                                    <i class="fas fa-file-pdf me-2"></i>PDF
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                        <?php elseif (empty($employees)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo $lang === 'ar' ? 'لا توجد بيانات موظفين' : 'Aucune donnée d\'employé'; ?>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover data-table">
                                <thead>
                                    <tr>
                                        <th><?php echo $lang === 'ar' ? 'رقم التسجيل' : 'N° Employé'; ?></th>
                                        <th><?php echo $lang === 'ar' ? 'الاسم الكامل' : 'Nom complet'; ?></th>
                                        <th><?php echo $lang === 'ar' ? 'المنصب' : 'Poste'; ?></th>
                                        <th><?php echo $lang === 'ar' ? 'القسم' : 'Département'; ?></th>
                                        <th><?php echo $lang === 'ar' ? 'تاريخ التوظيف' : 'Date embauche'; ?></th>
                                        <th><?php echo $lang === 'ar' ? 'الحالة' : 'Statut'; ?></th>
                                        <th class="no-sort"><?php echo $lang === 'ar' ? 'الإجراءات' : 'Actions'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($employees as $emp): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($emp['employee_number']); ?></strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <?php echo strtoupper(substr($emp['first_name_ar'], 0, 1)); ?>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo htmlspecialchars($emp['first_name_ar'] . ' ' . $emp['last_name_ar']); ?>
                                                    </div>
                                                    <?php if ($emp['email']): ?>
                                                    <small class="text-muted"><?php echo htmlspecialchars($emp['email']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($lang === 'ar' ? $emp['position_title_ar'] : $emp['position_title_fr']); ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($lang === 'ar' ? $emp['department_name_ar'] : $emp['department_name_fr']); ?>
                                        </td>
                                        <td class="date">
                                            <?php echo date('d/m/Y', strtotime($emp['hire_date'])); ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'active' => 'success',
                                                'inactive' => 'warning',
                                                'terminated' => 'danger',
                                                'retired' => 'secondary'
                                            ];
                                            $statusText = [
                                                'active' => $lang === 'ar' ? 'نشط' : 'Actif',
                                                'inactive' => $lang === 'ar' ? 'غير نشط' : 'Inactif',
                                                'terminated' => $lang === 'ar' ? 'منتهي الخدمة' : 'Terminé',
                                                'retired' => $lang === 'ar' ? 'متقاعد' : 'Retraité'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass[$emp['status']] ?? 'secondary'; ?>">
                                                <?php echo $statusText[$emp['status']] ?? $emp['status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="view.php?id=<?php echo $emp['id']; ?>" 
                                                   class="btn btn-outline-info" 
                                                   title="<?php echo $lang === 'ar' ? 'عرض' : 'Voir'; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $emp['id']; ?>" 
                                                   class="btn btn-outline-primary" 
                                                   title="<?php echo $lang === 'ar' ? 'تعديل' : 'Modifier'; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete.php?id=<?php echo $emp['id']; ?>" 
                                                   class="btn btn-outline-danger delete-btn" 
                                                   title="<?php echo $lang === 'ar' ? 'حذف' : 'Supprimer'; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../assets/js/main.js"></script>
    
    <style>
        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .table td {
            vertical-align: middle;
        }
        
        .badge {
            font-size: 0.75rem;
        }
    </style>
</body>
</html>
