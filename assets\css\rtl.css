/* أنماط اللغة العربية - RTL Styles */
/* Arabic Language Styles - Right to Left */

/* إعدادات RTL الأساسية */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl * {
    direction: rtl;
}

/* الخطوط العربية */
.rtl body,
.rtl .navbar,
.rtl .card,
.rtl .btn,
.rtl .form-control,
.rtl .table {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
}

/* التنقل RTL */
.rtl .navbar-nav {
    margin-right: auto;
    margin-left: 0;
}

.rtl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 1rem;
}

.rtl .navbar-toggler {
    margin-left: 0;
    margin-right: auto;
}

.rtl .dropdown-menu {
    right: 0;
    left: auto;
}

.rtl .dropdown-menu-end {
    right: auto;
    left: 0;
}

/* الأيقونات RTL */
.rtl .fas,
.rtl .far,
.rtl .fab {
    margin-left: 0.5rem;
    margin-right: 0;
}

.rtl .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.rtl .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.rtl .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.rtl .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.rtl .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.rtl .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

/* البطاقات RTL */
.rtl .card-header {
    text-align: right;
}

.rtl .card-body {
    text-align: right;
}

.rtl .card-title {
    text-align: right;
}

.rtl .card-text {
    text-align: right;
}

/* الجداول RTL */
.rtl .table {
    text-align: right;
}

.rtl .table th {
    text-align: right;
}

.rtl .table td {
    text-align: right;
}

.rtl .table .text-center {
    text-align: center !important;
}

.rtl .table .text-end {
    text-align: left !important;
}

.rtl .table .text-start {
    text-align: right !important;
}

/* النماذج RTL */
.rtl .form-label {
    text-align: right;
}

.rtl .form-control {
    text-align: right;
}

.rtl .form-select {
    text-align: right;
    background-position: left 0.75rem center;
}

.rtl .input-group-text {
    text-align: right;
}

.rtl .form-check {
    text-align: right;
}

.rtl .form-check-input {
    margin-left: 0;
    margin-right: -1.25em;
}

.rtl .form-check-label {
    padding-left: 0;
    padding-right: 1.25em;
}

/* الأزرار RTL */
.rtl .btn-group {
    direction: rtl;
}

.rtl .btn-toolbar {
    direction: rtl;
}

/* التنبيهات RTL */
.rtl .alert {
    text-align: right;
}

.rtl .alert-success {
    border-right: 4px solid var(--success-color);
    border-left: none;
}

.rtl .alert-danger {
    border-right: 4px solid var(--danger-color);
    border-left: none;
}

.rtl .alert-warning {
    border-right: 4px solid var(--warning-color);
    border-left: none;
}

.rtl .alert-info {
    border-right: 4px solid var(--info-color);
    border-left: none;
}

/* القوائم RTL */
.rtl .list-group-item {
    text-align: right;
}

.rtl .breadcrumb {
    direction: rtl;
}

.rtl .breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
}

/* الشبكة RTL */
.rtl .row {
    margin-right: -0.75rem;
    margin-left: -0.75rem;
}

.rtl .col,
.rtl [class*="col-"] {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* المحاذاة RTL */
.rtl .text-start {
    text-align: right !important;
}

.rtl .text-end {
    text-align: left !important;
}

.rtl .text-center {
    text-align: center !important;
}

.rtl .float-start {
    float: right !important;
}

.rtl .float-end {
    float: left !important;
}

/* الهوامش والحشو RTL */
.rtl .pe-0 {
    padding-left: 0 !important;
    padding-right: auto !important;
}

.rtl .pe-1 {
    padding-left: 0.25rem !important;
    padding-right: auto !important;
}

.rtl .pe-2 {
    padding-left: 0.5rem !important;
    padding-right: auto !important;
}

.rtl .pe-3 {
    padding-left: 1rem !important;
    padding-right: auto !important;
}

.rtl .ps-0 {
    padding-right: 0 !important;
    padding-left: auto !important;
}

.rtl .ps-1 {
    padding-right: 0.25rem !important;
    padding-left: auto !important;
}

.rtl .ps-2 {
    padding-right: 0.5rem !important;
    padding-left: auto !important;
}

.rtl .ps-3 {
    padding-right: 1rem !important;
    padding-left: auto !important;
}

/* الحدود RTL */
.rtl .border-start {
    border-right: 1px solid var(--border-color) !important;
    border-left: 0 !important;
}

.rtl .border-end {
    border-left: 1px solid var(--border-color) !important;
    border-right: 0 !important;
}

/* الموضع RTL */
.rtl .position-absolute {
    right: auto;
    left: auto;
}

.rtl .start-0 {
    right: 0 !important;
    left: auto !important;
}

.rtl .end-0 {
    left: 0 !important;
    right: auto !important;
}

.rtl .start-50 {
    right: 50% !important;
    left: auto !important;
}

.rtl .end-50 {
    left: 50% !important;
    right: auto !important;
}

/* التحويلات RTL */
.rtl .translate-middle-x {
    transform: translateX(50%) !important;
}

/* الفلكس RTL */
.rtl .justify-content-start {
    justify-content: flex-end !important;
}

.rtl .justify-content-end {
    justify-content: flex-start !important;
}

.rtl .align-items-start {
    align-items: flex-end !important;
}

.rtl .align-items-end {
    align-items: flex-start !important;
}

/* النصوص RTL */
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
    text-align: right;
}

.rtl p {
    text-align: right;
}

.rtl .lead {
    text-align: right;
}

.rtl .display-1,
.rtl .display-2,
.rtl .display-3,
.rtl .display-4,
.rtl .display-5,
.rtl .display-6 {
    text-align: right;
}

/* الأرقام العربية */
.rtl .arabic-numbers {
    font-feature-settings: "lnum";
    font-variant-numeric: lining-nums;
}

/* تحسينات الخط العربي */
.rtl {
    font-feature-settings: "kern", "liga", "clig", "calt";
    font-variant-ligatures: common-ligatures contextual;
    text-rendering: optimizeLegibility;
}

/* تباعد الأحرف العربية */
.rtl .letter-spacing-wide {
    letter-spacing: 0.05em;
}

.rtl .letter-spacing-wider {
    letter-spacing: 0.1em;
}

/* ارتفاع الخط العربي */
.rtl .line-height-relaxed {
    line-height: 1.8;
}

.rtl .line-height-loose {
    line-height: 2;
}

/* الاستجابة للشاشات الصغيرة RTL */
@media (max-width: 768px) {
    .rtl .navbar-nav {
        text-align: right;
    }
    
    .rtl .navbar-collapse {
        text-align: right;
    }
    
    .rtl .offcanvas-header {
        text-align: right;
    }
    
    .rtl .offcanvas-body {
        text-align: right;
    }
}

@media (max-width: 576px) {
    .rtl .hero-section h1 {
        text-align: center;
    }
    
    .rtl .hero-section .lead {
        text-align: center;
    }
    
    .rtl .card-body {
        text-align: right;
    }
}

/* تحسينات الطباعة RTL */
@media print {
    .rtl * {
        direction: rtl;
        text-align: right;
    }
    
    .rtl .table {
        direction: rtl;
    }
    
    .rtl .table th,
    .rtl .table td {
        text-align: right;
    }
}

/* تحسينات إضافية للعربية */
.rtl .arabic-text {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    line-height: 1.8;
    word-spacing: 0.1em;
}

.rtl .arabic-title {
    font-family: 'Cairo', 'Amiri', sans-serif;
    font-weight: 700;
    line-height: 1.4;
}

.rtl .arabic-body {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 400;
    line-height: 1.8;
}

/* تحسينات الأرقام */
.rtl .number-format {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

.rtl .currency {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

/* تحسينات التواريخ */
.rtl .date-format {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

/* تحسينات الجداول المالية */
.rtl .financial-table .amount {
    direction: ltr;
    text-align: left;
}

.rtl .financial-table .currency {
    direction: ltr;
    text-align: left;
}

.rtl .financial-table .percentage {
    direction: ltr;
    text-align: left;
}
