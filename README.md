# نظام إدارة فيش الراتب الجزائري
## Système de Gestion de Paie Algérien

نظام شامل ومتطور لإدارة فيش الراتب وفقاً للقوانين الجزائرية والضمان الاجتماعي والضرائب.

Système complet et avancé de gestion de paie conforme aux lois algériennes, à la sécurité sociale et à la fiscalité.

## المميزات الرئيسية | Fonctionnalités principales

### 🏢 إدارة الشركات والموظفين | Gestion des entreprises et employés
- إدارة بيانات الشركات والمؤسسات
- إدارة شاملة للموظفين والمناصب والأقسام
- نظام صلاحيات متقدم للمستخدمين
- تتبع تاريخ التوظيف والترقيات

### 💰 نظام الرواتب المتقدم | Système de paie avancé
- حساب تلقائي للرواتب الأساسية والمنح
- حساب دقيق للاستقطاعات (ضمان اجتماعي، ضرائب)
- دعم الساعات الإضافية والمكافآت
- إنتاج فيش راتب احترافية قابلة للطباعة

### 📊 التقارير والإحصائيات | Rapports et statistiques
- تقارير شهرية وسنوية مفصلة
- إحصائيات الضمان الاجتماعي والضرائب
- تصدير البيانات بصيغ متعددة (PDF, Excel, CSV)
- رسوم بيانية تفاعلية

### 🔒 الأمان والامتثال | Sécurité et conformité
- نظام أمان متقدم مع تشفير البيانات
- سجل مراجعة شامل لجميع العمليات
- امتثال كامل للقوانين الجزائرية
- نسخ احتياطية تلقائية

### 🌐 دعم متعدد اللغات | Support multilingue
- واجهة باللغة العربية والفرنسية
- دعم كامل للنصوص من اليمين إلى اليسار (RTL)
- تنسيق التواريخ والأرقام حسب المعايير المحلية

## المتطلبات التقنية | Exigences techniques

### الخادم | Serveur
- PHP 8.0 أو أحدث
- MySQL 8.0 أو MariaDB 10.4
- Apache 2.4 أو Nginx
- SSL Certificate (مُوصى به)

### المتصفحات المدعومة | Navigateurs supportés
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## التثبيت | Installation

### 1. تحميل الملفات | Téléchargement des fichiers
```bash
git clone https://github.com/your-repo/algerian-payroll-system.git
cd algerian-payroll-system
```

### 2. إعداد قاعدة البيانات | Configuration de la base de données
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE algerian_payroll CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'payroll_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON algerian_payroll.* TO 'payroll_user'@'localhost';
FLUSH PRIVILEGES;

-- استيراد الهيكل والبيانات الأولية
mysql -u payroll_user -p algerian_payroll < database/schema.sql
mysql -u payroll_user -p algerian_payroll < database/seed_data.sql
```

### 3. تكوين النظام | Configuration du système
```php
// نسخ ملف التكوين
cp config/config.php config/local_config.php

// تحديث إعدادات قاعدة البيانات في local_config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'algerian_payroll');
define('DB_USER', 'payroll_user');
define('DB_PASS', 'secure_password');

// تحديث مفتاح الأمان
define('SECRET_KEY', 'your-unique-secret-key-here');
```

### 4. إعداد الصلاحيات | Configuration des permissions
```bash
# إعداد صلاحيات المجلدات
chmod 755 uploads/ temp/ logs/ backups/
chmod 644 config/config.php

# إعداد ملكية الملفات (على Linux/Unix)
chown -R www-data:www-data /path/to/algerian-payroll-system/
```

## الاستخدام | Utilisation

### تسجيل الدخول الأولي | Connexion initiale
- **اسم المستخدم | Nom d'utilisateur:** admin
- **كلمة المرور | Mot de passe:** password

⚠️ **مهم:** يجب تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

### إعداد الشركة | Configuration de l'entreprise
1. انتقل إلى الإعدادات > بيانات الشركة
2. أدخل معلومات الشركة الكاملة
3. حدد أرقام التسجيل والضرائب
4. احفظ الإعدادات

### إضافة الموظفين | Ajout d'employés
1. انتقل إلى الموظفين > إضافة موظف
2. أدخل البيانات الشخصية والوظيفية
3. حدد المنصب والقسم
4. احفظ بيانات الموظف

### حساب الرواتب | Calcul des salaires
1. انتقل إلى الرواتب > حساب الرواتب
2. اختر فترة الراتب (الشهر والسنة)
3. حدد الموظفين أو اختر الكل
4. راجع النتائج واعتمدها

## القوانين الجزائرية المطبقة | Lois algériennes appliquées

### معدلات الضمان الاجتماعي | Taux de sécurité sociale
- **اشتراك الموظف | Cotisation employé:** 9%
- **اشتراك صاحب العمل | Cotisation employeur:** 26%
- **التأمين على البطالة | Assurance chômage:** 0.5% (موظف) + 1.5% (صاحب عمل)
- **التقاعد المسبق | Retraite anticipée:** 0.25% (كل طرف)

### ضريبة الدخل الإجمالي (IRG) | Impôt sur le Revenu Global
- **0 - 15,000 DA:** معفى | Exonéré
- **15,001 - 30,000 DA:** 20%
- **30,001 - 120,000 DA:** 30%
- **أكثر من 120,000 DA | Plus de 120,000 DA:** 35%

### الإعفاءات الضريبية | Exonérations fiscales
- **الإعفاء الشخصي | Exonération personnelle:** 15,000 DA
- **إعفاء الزوج/الزوجة | Exonération conjoint:** 3,000 DA
- **إعفاء لكل طفل | Exonération par enfant:** 1,800 DA (حد أقصى 6 أطفال)

## الدعم والصيانة | Support et maintenance

### النسخ الاحتياطية | Sauvegardes
- نسخ احتياطية تلقائية يومية
- إمكانية إنشاء نسخ احتياطية يدوية
- استعادة سهلة من النسخ الاحتياطية

### السجلات | Journaux
- سجل شامل لجميع العمليات
- تتبع تسجيل الدخول والخروج
- مراقبة التغييرات في البيانات

### الأمان | Sécurité
- تشفير كلمات المرور
- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- جلسات آمنة مع انتهاء صلاحية

## المساهمة | Contribution

نرحب بالمساهمات لتطوير النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص | Licence

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال | Contact

- **البريد الإلكتروني | Email:** <EMAIL>
- **الموقع الإلكتروني | Site web:** https://www.algerian-payroll.com
- **الدعم التقني | Support technique:** https://support.algerian-payroll.com

## إخلاء المسؤولية | Avertissement

هذا النظام مصمم للامتثال للقوانين الجزائرية الحالية. يُنصح بمراجعة محاسب قانوني أو خبير ضرائب للتأكد من الامتثال الكامل للقوانين المحلية.

Ce système est conçu pour se conformer aux lois algériennes actuelles. Il est recommandé de consulter un comptable agréé ou un expert fiscal pour assurer une conformité complète aux lois locales.

---

**الإصدار | Version:** 1.0.0  
**تاريخ آخر تحديث | Dernière mise à jour:** 2024-12-31
