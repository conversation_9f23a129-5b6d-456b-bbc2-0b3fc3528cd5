<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

require_once 'config/config.php';
require_once 'classes/Database.php';

session_start();

// إعادة توجيه إذا كان المستخدم مسجل دخوله بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$lang = isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES) ? $_GET['lang'] : DEFAULT_LANGUAGE;
$_SESSION['language'] = $lang;

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = $lang === 'ar' ? 'يرجى إدخال اسم المستخدم وكلمة المرور' : 'Veuillez saisir le nom d\'utilisateur et le mot de passe';
    } else {
        try {
            $db = Database::getInstance();
            
            // البحث عن المستخدم
            $user = $db->selectOne(
                "SELECT u.*, c.name_ar as company_name_ar, c.name_fr as company_name_fr 
                 FROM users u 
                 LEFT JOIN companies c ON u.company_id = c.id 
                 WHERE u.username = :username AND u.is_active = 1",
                ['username' => $username]
            );
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user'] = $user;
                $_SESSION['company_id'] = $user['company_id'];
                $_SESSION['role'] = $user['role'];
                
                // تحديث آخر تسجيل دخول
                $db->update('users', 
                    ['last_login' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $user['id']]
                );
                
                // تسجيل العملية في السجل
                $db->insert('audit_logs', [
                    'user_id' => $user['id'],
                    'action' => 'login',
                    'table_name' => 'users',
                    'record_id' => $user['id'],
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                // إعداد cookie للتذكر
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
                    // يمكن حفظ التوكن في قاعدة البيانات للتحقق لاحقاً
                }
                
                header('Location: dashboard.php');
                exit;
            } else {
                $error = $lang === 'ar' ? 'اسم المستخدم أو كلمة المرور غير صحيحة' : 'Nom d\'utilisateur ou mot de passe incorrect';
            }
        } catch (Exception $e) {
            $error = $lang === 'ar' ? 'حدث خطأ في النظام' : 'Erreur système';
            error_log('Login error: ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo in_array($lang, RTL_LANGUAGES) ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'تسجيل الدخول - ' . APP_NAME : 'Connexion - ' . APP_NAME_FR; ?></title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if ($lang === 'ar'): ?>
    <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #2c5530, #4a7c59);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-image {
            background: linear-gradient(135deg, rgba(44, 85, 48, 0.9), rgba(74, 124, 89, 0.9)),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 3rem;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #2c5530;
        }
        
        .form-control {
            padding: 0.875rem 1rem;
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #2c5530;
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #2c5530, #4a7c59);
            border: none;
            padding: 0.875rem 2rem;
            font-weight: 600;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #1e3a21, #3a6247);
            transform: translateY(-1px);
            box-shadow: 0 6px 12px rgba(44, 85, 48, 0.4);
        }
        
        .language-switcher {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }
        
        .rtl .language-switcher {
            right: auto;
            left: 1rem;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>">
    
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-globe me-1"></i>
                <?php echo $lang === 'ar' ? 'العربية' : 'Français'; ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                <li><a class="dropdown-item" href="?lang=fr">Français</a></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Login Image Section -->
                        <div class="col-lg-6 d-none d-lg-block">
                            <div class="login-image h-100">
                                <div>
                                    <i class="fas fa-file-invoice-dollar fa-5x mb-4"></i>
                                    <h2 class="mb-3">
                                        <?php echo $lang === 'ar' ? 'نظام إدارة الرواتب' : 'Système de Paie'; ?>
                                    </h2>
                                    <p class="lead">
                                        <?php echo $lang === 'ar' 
                                            ? 'نظام متطور وشامل لإدارة فيش الراتب وفقاً للقوانين الجزائرية'
                                            : 'Système avancé et complet de gestion de paie conforme aux lois algériennes'; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Login Form Section -->
                        <div class="col-lg-6">
                            <div class="login-form">
                                <div class="text-center mb-4">
                                    <div class="logo">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <h3 class="mb-2">
                                        <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Connexion'; ?>
                                    </h3>
                                    <p class="text-muted">
                                        <?php echo $lang === 'ar' 
                                            ? 'أدخل بياناتك للوصول إلى النظام'
                                            : 'Entrez vos données pour accéder au système'; ?>
                                    </p>
                                </div>
                                
                                <?php if ($error): ?>
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($success): ?>
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success); ?>
                                </div>
                                <?php endif; ?>
                                
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="username" class="form-label">
                                            <i class="fas fa-user me-2"></i>
                                            <?php echo $lang === 'ar' ? 'اسم المستخدم' : 'Nom d\'utilisateur'; ?>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="username" 
                                               name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                               required 
                                               autocomplete="username"
                                               placeholder="<?php echo $lang === 'ar' ? 'أدخل اسم المستخدم' : 'Entrez le nom d\'utilisateur'; ?>">
                                        <div class="invalid-feedback">
                                            <?php echo $lang === 'ar' ? 'يرجى إدخال اسم المستخدم' : 'Veuillez saisir le nom d\'utilisateur'; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock me-2"></i>
                                            <?php echo $lang === 'ar' ? 'كلمة المرور' : 'Mot de passe'; ?>
                                        </label>
                                        <div class="input-group">
                                            <input type="password" 
                                                   class="form-control" 
                                                   id="password" 
                                                   name="password" 
                                                   required 
                                                   autocomplete="current-password"
                                                   placeholder="<?php echo $lang === 'ar' ? 'أدخل كلمة المرور' : 'Entrez le mot de passe'; ?>">
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">
                                            <?php echo $lang === 'ar' ? 'يرجى إدخال كلمة المرور' : 'Veuillez saisir le mot de passe'; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            <?php echo $lang === 'ar' ? 'تذكرني' : 'Se souvenir de moi'; ?>
                                        </label>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary btn-login">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Se connecter'; ?>
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="text-center mt-4">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <i class="fas fa-question-circle me-1"></i>
                                        <?php echo $lang === 'ar' ? 'نسيت كلمة المرور؟' : 'Mot de passe oublié?'; ?>
                                    </a>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="text-center">
                                    <small class="text-muted">
                                        <?php echo $lang === 'ar' ? 'بيانات تجريبية:' : 'Données de test:'; ?>
                                        <br>
                                        <strong>admin</strong> / <strong>password</strong>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Auto-focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
