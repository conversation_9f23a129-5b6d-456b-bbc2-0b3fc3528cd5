<?php
/**
 * معالج التثبيت لنظام إدارة فيش الراتب الجزائري
 * Installation Handler for Algerian Payroll Management System
 */

// التحقق من وجود ملف التكوين
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
} else {
    die('ملف التكوين غير موجود. يرجى نسخ config.php من المجلد config/');
}

$step = $_GET['step'] ?? 1;
$lang = $_GET['lang'] ?? 'ar';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // التحقق من المتطلبات
            $requirements = checkRequirements();
            if ($requirements['all_passed']) {
                header('Location: install.php?step=3&lang=' . $lang);
                exit;
            }
            break;
            
        case 3:
            // إعداد قاعدة البيانات
            $dbResult = setupDatabase($_POST);
            if ($dbResult['success']) {
                header('Location: install.php?step=4&lang=' . $lang);
                exit;
            }
            break;
            
        case 4:
            // إنشاء المستخدم الإداري
            $adminResult = createAdminUser($_POST);
            if ($adminResult['success']) {
                header('Location: install.php?step=5&lang=' . $lang);
                exit;
            }
            break;
    }
}

/**
 * التحقق من المتطلبات
 */
function checkRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'mbstring' => extension_loaded('mbstring'),
        'openssl' => extension_loaded('openssl'),
        'curl' => extension_loaded('curl'),
        'gd' => extension_loaded('gd'),
        'zip' => extension_loaded('zip'),
        'json' => extension_loaded('json'),
        'uploads_writable' => is_writable('uploads') || mkdir('uploads', 0755, true),
        'temp_writable' => is_writable('temp') || mkdir('temp', 0755, true),
        'logs_writable' => is_writable('logs') || mkdir('logs', 0755, true),
        'backups_writable' => is_writable('backups') || mkdir('backups', 0755, true)
    ];
    
    $requirements['all_passed'] = !in_array(false, $requirements);
    return $requirements;
}

/**
 * إعداد قاعدة البيانات
 */
function setupDatabase($data) {
    try {
        // الاتصال بقاعدة البيانات
        $dsn = "mysql:host={$data['db_host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $data['db_user'], $data['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$data['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$data['db_name']}`");
        
        // تنفيذ ملف الهيكل
        $schema = file_get_contents('database/schema.sql');
        $pdo->exec($schema);
        
        // تنفيذ البيانات الأولية
        $seedData = file_get_contents('database/seed_data.sql');
        $pdo->exec($seedData);
        
        // حفظ إعدادات قاعدة البيانات
        $configContent = "<?php\n";
        $configContent .= "// إعدادات قاعدة البيانات - تم إنشاؤها تلقائياً\n";
        $configContent .= "define('DB_HOST', '{$data['db_host']}');\n";
        $configContent .= "define('DB_NAME', '{$data['db_name']}');\n";
        $configContent .= "define('DB_USER', '{$data['db_user']}');\n";
        $configContent .= "define('DB_PASS', '{$data['db_pass']}');\n";
        $configContent .= "define('SECRET_KEY', '" . bin2hex(random_bytes(32)) . "');\n";
        
        file_put_contents('config/local_config.php', $configContent);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * إنشاء المستخدم الإداري
 */
function createAdminUser($data) {
    try {
        require_once 'classes/Database.php';
        $db = Database::getInstance();
        
        // التحقق من عدم وجود مستخدم إداري
        $existingAdmin = $db->selectOne("SELECT id FROM users WHERE role = 'admin'");
        if ($existingAdmin) {
            return ['success' => false, 'error' => 'يوجد مستخدم إداري بالفعل'];
        }
        
        // إنشاء المستخدم الإداري
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $userId = $db->insert('users', [
            'username' => $data['username'],
            'email' => $data['email'],
            'password_hash' => $hashedPassword,
            'full_name_ar' => $data['full_name_ar'],
            'full_name_fr' => $data['full_name_fr'],
            'role' => 'admin',
            'company_id' => 1,
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // إنشاء ملف علامة التثبيت
        file_put_contents('.installed', date('Y-m-d H:i:s'));
        
        return ['success' => true, 'user_id' => $userId];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'تثبيت نظام إدارة الرواتب' : 'Installation du Système de Paie'; ?></title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: <?php echo $lang === 'ar' ? "'Cairo', sans-serif" : "'Roboto', sans-serif"; ?>;
            background: linear-gradient(135deg, #2c5530, #4a7c59);
            min-height: 100vh;
            direction: <?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>;
        }
        
        .install-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #2c5530, #4a7c59);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background: #2c5530;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 50%;
            <?php echo $lang === 'ar' ? 'right' : 'left'; ?>: 100%;
            width: 2rem;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirement-item:last-child {
            border-bottom: none;
        }
        
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .status-pass {
            background: #28a745;
        }
        
        .status-fail {
            background: #dc3545;
        }
        
        .language-switcher {
            position: absolute;
            top: 1rem;
            <?php echo $lang === 'ar' ? 'left' : 'right'; ?>: 1rem;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="btn-group">
            <a href="?step=<?php echo $step; ?>&lang=ar" 
               class="btn btn-outline-light btn-sm <?php echo $lang === 'ar' ? 'active' : ''; ?>">
                العربية
            </a>
            <a href="?step=<?php echo $step; ?>&lang=fr" 
               class="btn btn-outline-light btn-sm <?php echo $lang === 'fr' ? 'active' : ''; ?>">
                Français
            </a>
        </div>
    </div>

    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1>
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    <?php echo $lang === 'ar' ? 'تثبيت نظام إدارة الرواتب' : 'Installation du Système de Paie'; ?>
                </h1>
                <p class="mb-0">
                    <?php echo $lang === 'ar' ? 'نظام شامل ومتطور وفقاً للقوانين الجزائرية' : 'Système complet et avancé conforme aux lois algériennes'; ?>
                </p>
            </div>
            
            <div class="install-body">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                    <div class="step <?php echo $i == $step ? 'active' : ($i < $step ? 'completed' : ''); ?>">
                        <?php if ($i < $step): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <?php echo $i; ?>
                        <?php endif; ?>
                    </div>
                    <?php endfor; ?>
                </div>
                
                <?php if ($step == 1): ?>
                <!-- Step 1: Welcome -->
                <div class="text-center">
                    <h3><?php echo $lang === 'ar' ? 'مرحباً بك' : 'Bienvenue'; ?></h3>
                    <p class="lead">
                        <?php echo $lang === 'ar' 
                            ? 'سيقوم هذا المعالج بإرشادك خلال عملية تثبيت نظام إدارة فيش الراتب الجزائري'
                            : 'Cet assistant vous guidera dans l\'installation du Système de Gestion de Paie Algérien'; ?>
                    </p>
                    
                    <div class="alert alert-info">
                        <h5><?php echo $lang === 'ar' ? 'قبل البدء، تأكد من:' : 'Avant de commencer, assurez-vous de:'; ?></h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>
                                <?php echo $lang === 'ar' ? 'وجود خادم ويب (Apache/Nginx)' : 'Avoir un serveur web (Apache/Nginx)'; ?>
                            </li>
                            <li><i class="fas fa-check text-success me-2"></i>
                                <?php echo $lang === 'ar' ? 'PHP 8.0 أو أحدث' : 'PHP 8.0 ou plus récent'; ?>
                            </li>
                            <li><i class="fas fa-check text-success me-2"></i>
                                <?php echo $lang === 'ar' ? 'MySQL 8.0 أو MariaDB 10.4' : 'MySQL 8.0 ou MariaDB 10.4'; ?>
                            </li>
                            <li><i class="fas fa-check text-success me-2"></i>
                                <?php echo $lang === 'ar' ? 'بيانات الاتصال بقاعدة البيانات' : 'Informations de connexion à la base de données'; ?>
                            </li>
                        </ul>
                    </div>
                    
                    <form method="POST" action="?step=2&lang=<?php echo $lang; ?>">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <?php echo $lang === 'ar' ? 'البدء في التثبيت' : 'Commencer l\'installation'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-2"></i>
                        </button>
                    </form>
                </div>
                
                <?php elseif ($step == 2): ?>
                <!-- Step 2: Requirements Check -->
                <h3><?php echo $lang === 'ar' ? 'فحص المتطلبات' : 'Vérification des exigences'; ?></h3>
                
                <?php $requirements = checkRequirements(); ?>
                
                <div class="requirements-list">
                    <div class="requirement-item">
                        <span><?php echo $lang === 'ar' ? 'إصدار PHP (8.0+)' : 'Version PHP (8.0+)'; ?></span>
                        <div class="status-icon <?php echo $requirements['php_version'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['php_version'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span>PDO MySQL Extension</span>
                        <div class="status-icon <?php echo $requirements['pdo_mysql'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['pdo_mysql'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span>Mbstring Extension</span>
                        <div class="status-icon <?php echo $requirements['mbstring'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['mbstring'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span>OpenSSL Extension</span>
                        <div class="status-icon <?php echo $requirements['openssl'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['openssl'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span>cURL Extension</span>
                        <div class="status-icon <?php echo $requirements['curl'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['curl'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span>GD Extension</span>
                        <div class="status-icon <?php echo $requirements['gd'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['gd'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span><?php echo $lang === 'ar' ? 'مجلد uploads قابل للكتابة' : 'Dossier uploads accessible en écriture'; ?></span>
                        <div class="status-icon <?php echo $requirements['uploads_writable'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['uploads_writable'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="requirement-item">
                        <span><?php echo $lang === 'ar' ? 'مجلد temp قابل للكتابة' : 'Dossier temp accessible en écriture'; ?></span>
                        <div class="status-icon <?php echo $requirements['temp_writable'] ? 'status-pass' : 'status-fail'; ?>">
                            <i class="fas fa-<?php echo $requirements['temp_writable'] ? 'check' : 'times'; ?>"></i>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <?php if ($requirements['all_passed']): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $lang === 'ar' ? 'جميع المتطلبات متوفرة!' : 'Toutes les exigences sont satisfaites!'; ?>
                    </div>
                    
                    <form method="POST">
                        <button type="submit" class="btn btn-primary">
                            <?php echo $lang === 'ar' ? 'المتابعة' : 'Continuer'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-2"></i>
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $lang === 'ar' 
                            ? 'يرجى حل المشاكل المذكورة أعلاه قبل المتابعة'
                            : 'Veuillez résoudre les problèmes mentionnés ci-dessus avant de continuer'; ?>
                    </div>
                    
                    <button onclick="location.reload()" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i>
                        <?php echo $lang === 'ar' ? 'إعادة الفحص' : 'Revérifier'; ?>
                    </button>
                    <?php endif; ?>
                </div>
                
                <?php elseif ($step == 5): ?>
                <!-- Step 5: Complete -->
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h3 class="text-success">
                        <?php echo $lang === 'ar' ? 'تم التثبيت بنجاح!' : 'Installation réussie!'; ?>
                    </h3>
                    
                    <p class="lead">
                        <?php echo $lang === 'ar' 
                            ? 'تم تثبيت نظام إدارة فيش الراتب الجزائري بنجاح'
                            : 'Le Système de Gestion de Paie Algérien a été installé avec succès'; ?>
                    </p>
                    
                    <div class="alert alert-warning">
                        <h5><?php echo $lang === 'ar' ? 'مهم للأمان:' : 'Important pour la sécurité:'; ?></h5>
                        <ul class="list-unstyled">
                            <li><?php echo $lang === 'ar' ? '• احذف ملف install.php' : '• Supprimez le fichier install.php'; ?></li>
                            <li><?php echo $lang === 'ar' ? '• غير كلمة المرور الافتراضية' : '• Changez le mot de passe par défaut'; ?></li>
                            <li><?php echo $lang === 'ar' ? '• فعل SSL في الإنتاج' : '• Activez SSL en production'; ?></li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-block">
                        <a href="login.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <?php echo $lang === 'ar' ? 'تسجيل الدخول' : 'Se connecter'; ?>
                        </a>
                        <a href="README.md" class="btn btn-outline-secondary btn-lg" target="_blank">
                            <i class="fas fa-book me-2"></i>
                            <?php echo $lang === 'ar' ? 'دليل المستخدم' : 'Guide utilisateur'; ?>
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
