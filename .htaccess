# نظام إدارة فيش الراتب الجزائري - Apache Configuration
# Algerian Payroll Management System - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول إلى الملفات الحساسة
<FilesMatch "\.(env|log|sql|md|json|lock|xml|yml|yaml)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى مجلدات النظام
RedirectMatch 403 ^/?(config|classes|includes|logs|backups|temp|vendor|tests|scripts)/.*$

# منع الوصول إلى ملفات PHP في مجلدات معينة
<Directory "config">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

<Directory "classes">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

<Directory "includes">
    <Files "*.php">
        Order Allow,Deny
        Allow from 127.0.0.1
        Allow from ::1
    </Files>
</Directory>

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# منع الوصول إلى ملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعدادات الأمان للعناوين
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # سياسة الأمان للمحتوى
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # سياسة الإحالة
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # إعدادات HTTPS (في حالة استخدام SSL)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML و PHP
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/x-httpd-php "access plus 1 hour"
</IfModule>

# إعدادات PHP الأمنية
<IfModule mod_php8.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    php_flag expose_php Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_only_cookies 1
    php_value session.cookie_samesite Strict
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
    php_value post_max_size 10M
    php_value upload_max_filesize 5M
    php_value max_file_uploads 10
</IfModule>

# منع الوصول إلى ملفات Git
<FilesMatch "^\.git">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات Composer
<FilesMatch "^composer\.(json|lock)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات التكوين
<FilesMatch "^(\.env|\.htaccess|\.htpasswd)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعادة توجيه الأخطاء
ErrorDocument 403 /error/403.php
ErrorDocument 404 /error/404.php
ErrorDocument 500 /error/500.php

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات MIME
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>

# حد معدل الطلبات (إذا كان mod_evasive متاحاً)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# منع الهجمات على ملفات XML
<Files "*.xml">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول إلى ملفات معينة فقط
<Files "sitemap.xml">
    Order Allow,Deny
    Allow from all
</Files>

# إعدادات خاصة بالتطبيق
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# إعادة توجيه HTTP إلى HTTPS (في حالة استخدام SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# منع الوصول المباشر إلى ملفات PHP في مجلد uploads
<Directory "uploads">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

# إعدادات خاصة بالجلسات
<IfModule mod_php8.c>
    php_value session.save_path "temp/sessions"
    php_value session.gc_maxlifetime 3600
    php_value session.gc_probability 1
    php_value session.gc_divisor 100
</IfModule>
