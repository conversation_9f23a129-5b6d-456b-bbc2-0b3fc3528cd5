<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

require_once 'config/config.php';
require_once 'classes/Database.php';
require_once 'classes/Employee.php';

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$lang = $_SESSION['language'] ?? DEFAULT_LANGUAGE;
$user = $_SESSION['user'];

// الحصول على الإحصائيات
try {
    $db = Database::getInstance();
    $employee = new Employee();
    
    // إحصائيات الموظفين
    $employeeStats = $employee->getEmployeeStats();
    
    // إحصائيات الرواتب
    $payrollStats = $db->selectOne("
        SELECT 
            COUNT(*) as total_payrolls,
            SUM(net_salary) as total_net_salary,
            AVG(net_salary) as avg_net_salary
        FROM payrolls p
        JOIN payroll_periods pp ON p.payroll_period_id = pp.id
        WHERE pp.year = YEAR(NOW()) AND pp.month = MONTH(NOW())
    ");
    
    // إحصائيات هذا الشهر
    $currentMonthStats = $db->selectOne("
        SELECT 
            COUNT(DISTINCT p.employee_id) as employees_paid,
            SUM(p.gross_salary) as total_gross,
            SUM(p.total_deductions) as total_deductions,
            SUM(p.net_salary) as total_net
        FROM payrolls p
        JOIN payroll_periods pp ON p.payroll_period_id = pp.id
        WHERE pp.year = YEAR(NOW()) AND pp.month = MONTH(NOW())
        AND p.status = 'approved'
    ");
    
    // أحدث العمليات
    $recentActivities = $db->select("
        SELECT 
            al.*,
            u.full_name_ar,
            u.full_name_fr
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 10
    ");
    
    // الموظفين الجدد هذا الشهر
    $newEmployees = $db->select("
        SELECT 
            e.*,
            p.title_ar as position_title_ar,
            p.title_fr as position_title_fr,
            d.name_ar as department_name_ar,
            d.name_fr as department_name_fr
        FROM employees e
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE e.status = 'active'
        AND YEAR(e.hire_date) = YEAR(NOW())
        AND MONTH(e.hire_date) = MONTH(NOW())
        ORDER BY e.hire_date DESC
        LIMIT 5
    ");
    
    // المعاملات المعلقة
    $pendingPayrolls = $db->select("
        SELECT 
            p.*,
            e.first_name_ar,
            e.last_name_ar,
            e.employee_number,
            pp.year,
            pp.month
        FROM payrolls p
        JOIN employees e ON p.employee_id = e.id
        JOIN payroll_periods pp ON p.payroll_period_id = pp.id
        WHERE p.status IN ('draft', 'calculated')
        ORDER BY pp.year DESC, pp.month DESC, e.first_name_ar
        LIMIT 10
    ");
    
} catch (Exception $e) {
    error_log('Dashboard error: ' . $e->getMessage());
    $employeeStats = ['active_employees' => 0, 'new_employees_this_month' => 0];
    $payrollStats = ['total_payrolls' => 0, 'total_net_salary' => 0, 'avg_net_salary' => 0];
    $currentMonthStats = ['employees_paid' => 0, 'total_gross' => 0, 'total_deductions' => 0, 'total_net' => 0];
    $recentActivities = [];
    $newEmployees = [];
    $pendingPayrolls = [];
}

$pageTitle = $lang === 'ar' ? 'لوحة التحكم' : 'Tableau de bord';
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo in_array($lang, RTL_LANGUAGES) ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . ($lang === 'ar' ? APP_NAME : APP_NAME_FR); ?></title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if ($lang === 'ar'): ?>
    <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="<?php echo $lang === 'ar' ? 'rtl' : 'ltr'; ?>">
    
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <?php echo $lang === 'ar' ? 'مرحباً، ' . $user['full_name_ar'] : 'Bienvenue, ' . $user['full_name_fr']; ?>
                        </h1>
                        <p class="text-muted mb-0">
                            <?php echo $lang === 'ar' ? 'إليك نظرة عامة على النظام' : 'Voici un aperçu du système'; ?>
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="current-datetime text-muted small"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">
                                    <?php echo $lang === 'ar' ? 'الموظفين النشطين' : 'Employés actifs'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo number_format($employeeStats['active_employees']); ?></h3>
                                <small class="opacity-75">
                                    +<?php echo $employeeStats['new_employees_this_month']; ?> 
                                    <?php echo $lang === 'ar' ? 'هذا الشهر' : 'ce mois'; ?>
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="employees/list.php" class="text-white text-decoration-none small">
                            <?php echo $lang === 'ar' ? 'عرض التفاصيل' : 'Voir détails'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">
                                    <?php echo $lang === 'ar' ? 'فيش الراتب هذا الشهر' : 'Bulletins ce mois'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo number_format($payrollStats['total_payrolls'] ?? 0); ?></h3>
                                <small class="opacity-75">
                                    <?php echo number_format($currentMonthStats['employees_paid'] ?? 0); ?> 
                                    <?php echo $lang === 'ar' ? 'موظف مدفوع' : 'employés payés'; ?>
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file-invoice-dollar fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="payroll/list.php" class="text-white text-decoration-none small">
                            <?php echo $lang === 'ar' ? 'عرض التفاصيل' : 'Voir détails'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">
                                    <?php echo $lang === 'ar' ? 'إجمالي الرواتب' : 'Total salaires'; ?>
                                </h6>
                                <h3 class="mb-0">
                                    <?php echo number_format($currentMonthStats['total_net'] ?? 0, 0, '.', ','); ?> DA
                                </h3>
                                <small class="opacity-75">
                                    <?php echo $lang === 'ar' ? 'هذا الشهر' : 'ce mois'; ?>
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="reports/monthly.php" class="text-white text-decoration-none small">
                            <?php echo $lang === 'ar' ? 'عرض التقرير' : 'Voir rapport'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">
                                    <?php echo $lang === 'ar' ? 'المعاملات المعلقة' : 'Transactions en attente'; ?>
                                </h6>
                                <h3 class="mb-0"><?php echo count($pendingPayrolls); ?></h3>
                                <small class="opacity-75">
                                    <?php echo $lang === 'ar' ? 'تحتاج مراجعة' : 'nécessitent révision'; ?>
                                </small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="payroll/pending.php" class="text-white text-decoration-none small">
                            <?php echo $lang === 'ar' ? 'مراجعة المعاملات' : 'Réviser transactions'; ?>
                            <i class="fas fa-arrow-<?php echo $lang === 'ar' ? 'left' : 'right'; ?> ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Quick Actions -->
        <div class="row mb-4">
            <!-- Salary Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            <?php echo $lang === 'ar' ? 'تطور الرواتب الشهرية' : 'Évolution des salaires mensuels'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salaryChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            <?php echo $lang === 'ar' ? 'الإجراءات السريعة' : 'Actions rapides'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="employees/add.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                <?php echo $lang === 'ar' ? 'إضافة موظف جديد' : 'Ajouter nouvel employé'; ?>
                            </a>
                            <a href="payroll/calculate.php" class="btn btn-outline-success">
                                <i class="fas fa-calculator me-2"></i>
                                <?php echo $lang === 'ar' ? 'حساب الرواتب' : 'Calculer salaires'; ?>
                            </a>
                            <a href="reports/monthly.php" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-2"></i>
                                <?php echo $lang === 'ar' ? 'تقرير شهري' : 'Rapport mensuel'; ?>
                            </a>
                            <a href="backup/create.php" class="btn btn-outline-secondary">
                                <i class="fas fa-database me-2"></i>
                                <?php echo $lang === 'ar' ? 'نسخة احتياطية' : 'Sauvegarde'; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities and New Employees -->
        <div class="row">
            <!-- Recent Activities -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            <?php echo $lang === 'ar' ? 'النشاطات الأخيرة' : 'Activités récentes'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentActivities)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-info-circle fa-2x mb-2"></i>
                            <p><?php echo $lang === 'ar' ? 'لا توجد أنشطة حديثة' : 'Aucune activité récente'; ?></p>
                        </div>
                        <?php else: ?>
                        <div class="timeline">
                            <?php foreach (array_slice($recentActivities, 0, 5) as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">
                                        <?php 
                                        $actions = [
                                            'create' => $lang === 'ar' ? 'إنشاء' : 'Création',
                                            'update' => $lang === 'ar' ? 'تحديث' : 'Mise à jour',
                                            'delete' => $lang === 'ar' ? 'حذف' : 'Suppression',
                                            'login' => $lang === 'ar' ? 'تسجيل دخول' : 'Connexion'
                                        ];
                                        echo $actions[$activity['action']] ?? $activity['action'];
                                        ?>
                                        <?php echo $activity['table_name']; ?>
                                    </h6>
                                    <p class="text-muted mb-1">
                                        <?php echo $lang === 'ar' ? 'بواسطة: ' : 'Par: '; ?>
                                        <?php echo $lang === 'ar' ? $activity['full_name_ar'] : $activity['full_name_fr']; ?>
                                    </p>
                                    <small class="text-muted">
                                        <?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <a href="logs/audit.php" class="btn btn-sm btn-outline-primary">
                            <?php echo $lang === 'ar' ? 'عرض جميع الأنشطة' : 'Voir toutes les activités'; ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- New Employees -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            <?php echo $lang === 'ar' ? 'الموظفين الجدد' : 'Nouveaux employés'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($newEmployees)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <p><?php echo $lang === 'ar' ? 'لا يوجد موظفين جدد هذا الشهر' : 'Aucun nouvel employé ce mois'; ?></p>
                        </div>
                        <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($newEmployees as $emp): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                        <?php echo strtoupper(substr($emp['first_name_ar'], 0, 1)); ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($emp['first_name_ar'] . ' ' . $emp['last_name_ar']); ?>
                                        </h6>
                                        <p class="text-muted mb-1">
                                            <?php echo htmlspecialchars($lang === 'ar' ? $emp['position_title_ar'] : $emp['position_title_fr']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo date('d/m/Y', strtotime($emp['hire_date'])); ?>
                                        </small>
                                    </div>
                                    <div>
                                        <a href="employees/view.php?id=<?php echo $emp['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <?php echo $lang === 'ar' ? 'عرض' : 'Voir'; ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <a href="employees/list.php" class="btn btn-sm btn-outline-primary">
                            <?php echo $lang === 'ar' ? 'عرض جميع الموظفين' : 'Voir tous les employés'; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <style>
        .timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .timeline-marker {
            position: absolute;
            left: -2rem;
            top: 0.25rem;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }
        
        .timeline-item:not(:last-child)::before {
            content: '';
            position: absolute;
            left: -1.625rem;
            top: 1rem;
            width: 2px;
            height: calc(100% + 0.5rem);
            background-color: #dee2e6;
        }
        
        .avatar-sm {
            width: 40px;
            height: 40px;
            font-size: 16px;
            font-weight: 600;
        }
    </style>
</body>
</html>
