<?php
/**
 * تسجيل الخروج
 * Logout
 */

require_once 'config/config.php';
require_once 'classes/Database.php';

session_start();

// تسجيل عملية تسجيل الخروج في السجل
if (isset($_SESSION['user_id'])) {
    try {
        $db = Database::getInstance();
        $db->insert('audit_logs', [
            'user_id' => $_SESSION['user_id'],
            'action' => 'logout',
            'table_name' => 'users',
            'record_id' => $_SESSION['user_id'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log('Logout logging error: ' . $e->getMessage());
    }
}

// مسح جميع بيانات الجلسة
$_SESSION = array();

// حذف cookie الجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// حذف cookie التذكر إذا كان موجوداً
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
}

// إنهاء الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?logout=1');
exit;
?>
