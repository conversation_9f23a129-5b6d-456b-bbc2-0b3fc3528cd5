{"name": "algerian-payroll/payroll-management-system", "description": "نظام إدارة فيش الراتب الجزائري - Système de Gestion de Paie Algérien", "type": "project", "keywords": ["payroll", "algeria", "salary", "social-security", "tax", "hrms", "paie", "algerie", "salaire", "securite-sociale", "impot"], "homepage": "https://github.com/algerian-payroll/payroll-management-system", "license": "MIT", "authors": [{"name": "Algerian Payroll Team", "email": "<EMAIL>", "homepage": "https://www.algerian-payroll.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/algerian-payroll/payroll-management-system/issues", "docs": "https://docs.algerian-payroll.com"}, "require": {"php": ">=8.0", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-curl": "*", "ext-gd": "*", "ext-zip": "*", "phpmailer/phpmailer": "^6.8", "dompdf/dompdf": "^2.0", "phpoffice/phpspreadsheet": "^1.29", "monolog/monolog": "^3.4", "vlucas/phpdotenv": "^5.5", "firebase/php-jwt": "^6.8", "respect/validation": "^2.2", "twig/twig": "^3.7"}, "require-dev": {"phpunit/phpunit": "^10.3", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.7", "friendsofphp/php-cs-fixer": "^3.22", "phpmd/phpmd": "^2.13", "vimeo/psalm": "^5.15"}, "autoload": {"psr-4": {"AlgerianPayroll\\": "src/", "AlgerianPayroll\\Classes\\": "classes/", "AlgerianPayroll\\Controllers\\": "controllers/", "AlgerianPayroll\\Models\\": "models/", "AlgerianPayroll\\Services\\": "services/", "AlgerianPayroll\\Utils\\": "utils/"}, "files": ["config/config.php"]}, "autoload-dev": {"psr-4": {"AlgerianPayroll\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "analyse": "phpstan analyse", "cs-check": "phpcs", "cs-fix": "phpcbf", "psalm": "psalm", "quality": ["@cs-check", "@analyse", "@psalm"], "install-db": ["php scripts/install-database.php"], "seed-db": ["php scripts/seed-database.php"], "backup-db": ["php scripts/backup-database.php"], "clear-cache": ["php scripts/clear-cache.php"], "post-install-cmd": ["@install-db"], "post-update-cmd": ["@clear-cache"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "archive": {"exclude": ["/tests", "/docs", "/.github", "/.giti<PERSON>re", "/phpunit.xml", "/phpstan.neon", "/psalm.xml", "/.php-cs-fixer.php"]}}