<?php
/**
 * نظام إدارة فيش الراتب الجزائري
 * Algerian Payroll Management System
 * 
 * ملف التكوين الرئيسي
 * Main Configuration File
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'algerian_payroll');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق
define('APP_NAME', 'نظام إدارة فيش الراتب الجزائري');
define('APP_NAME_FR', 'Système de Gestion de Paie Algérien');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/algerian-payroll');
define('APP_TIMEZONE', 'Africa/Algiers');

// إعدادات الأمان
define('SECRET_KEY', 'your-secret-key-here-change-in-production');
define('SESSION_LIFETIME', 3600); // ساعة واحدة
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 دقيقة

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'fr']);
define('RTL_LANGUAGES', ['ar']);

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// إعدادات البريد الإلكتروني
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'نظام إدارة الرواتب');

// إعدادات الضمان الاجتماعي الجزائري
define('SOCIAL_SECURITY_RATES', [
    'employee' => [
        'social_security' => 0.09,      // 9%
        'unemployment' => 0.005,        // 0.5%
        'early_retirement' => 0.0025    // 0.25%
    ],
    'employer' => [
        'social_security' => 0.26,      // 26%
        'unemployment' => 0.015,        // 1.5%
        'early_retirement' => 0.0025,   // 0.25%
        'work_accidents' => 0.0125      // 1.25%
    ]
]);

// جدول ضريبة الدخل الإجمالي (IRG) 2024
define('IRG_TAX_BRACKETS', [
    ['min' => 0, 'max' => 15000, 'rate' => 0.00],
    ['min' => 15001, 'max' => 30000, 'rate' => 0.20],
    ['min' => 30001, 'max' => 120000, 'rate' => 0.30],
    ['min' => 120001, 'max' => PHP_INT_MAX, 'rate' => 0.35]
]);

// الإعفاءات الضريبية
define('TAX_EXEMPTIONS', [
    'personal' => 15000,        // الإعفاء الشخصي
    'spouse' => 3000,           // إعفاء الزوج/الزوجة
    'child' => 1800,            // إعفاء لكل طفل
    'max_children' => 6         // الحد الأقصى للأطفال
]);

// الحد الأدنى والأقصى للأجور
define('MIN_WAGE', 20000);      // الأجر الأدنى المضمون
define('MAX_SOCIAL_SECURITY_BASE', 45000); // الحد الأقصى لأساس الضمان الاجتماعي

// إعدادات التقارير
define('REPORTS_PATH', 'reports/');
define('TEMP_PATH', 'temp/');

// إعدادات النسخ الاحتياطي
define('BACKUP_PATH', 'backups/');
define('AUTO_BACKUP', true);
define('BACKUP_RETENTION_DAYS', 30);

// إعدادات السجلات
define('LOG_PATH', 'logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10 ميجابايت

// إعدادات التطوير
define('DEBUG_MODE', true);
define('SHOW_ERRORS', true);
define('LOG_QUERIES', false);

// تعيين المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// إعدادات الجلسة
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // تعيين إلى 1 في الإنتاج مع HTTPS

// إعدادات PHP
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
ini_set('upload_max_filesize', '5M');
ini_set('post_max_size', '10M');

// إعدادات الأخطاء
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * دالة للحصول على إعدادات قاعدة البيانات
 */
function getDatabaseConfig() {
    return [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => DB_CHARSET,
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
        ]
    ];
}

/**
 * دالة لحساب ضريبة الدخل الإجمالي
 */
function calculateIRG($taxableIncome, $exemptions = 0) {
    $netTaxableIncome = max(0, $taxableIncome - $exemptions);
    $tax = 0;
    
    foreach (IRG_TAX_BRACKETS as $bracket) {
        if ($netTaxableIncome > $bracket['min']) {
            $taxableInThisBracket = min($netTaxableIncome, $bracket['max']) - $bracket['min'] + 1;
            $tax += $taxableInThisBracket * $bracket['rate'];
        }
    }
    
    return round($tax, 2);
}

/**
 * دالة لحساب الإعفاءات الضريبية
 */
function calculateTaxExemptions($maritalStatus, $childrenCount) {
    $exemptions = TAX_EXEMPTIONS['personal'];
    
    if ($maritalStatus === 'married') {
        $exemptions += TAX_EXEMPTIONS['spouse'];
    }
    
    $eligibleChildren = min($childrenCount, TAX_EXEMPTIONS['max_children']);
    $exemptions += $eligibleChildren * TAX_EXEMPTIONS['child'];
    
    return $exemptions;
}

/**
 * دالة لحساب اشتراكات الضمان الاجتماعي
 */
function calculateSocialSecurityContributions($salary) {
    $base = min($salary, MAX_SOCIAL_SECURITY_BASE);
    
    return [
        'employee' => [
            'social_security' => round($base * SOCIAL_SECURITY_RATES['employee']['social_security'], 2),
            'unemployment' => round($base * SOCIAL_SECURITY_RATES['employee']['unemployment'], 2),
            'early_retirement' => round($base * SOCIAL_SECURITY_RATES['employee']['early_retirement'], 2)
        ],
        'employer' => [
            'social_security' => round($base * SOCIAL_SECURITY_RATES['employer']['social_security'], 2),
            'unemployment' => round($base * SOCIAL_SECURITY_RATES['employer']['unemployment'], 2),
            'early_retirement' => round($base * SOCIAL_SECURITY_RATES['employer']['early_retirement'], 2),
            'work_accidents' => round($base * SOCIAL_SECURITY_RATES['employer']['work_accidents'], 2)
        ]
    ];
}

/**
 * دالة لتنسيق المبالغ المالية
 */
function formatCurrency($amount, $currency = 'DZD') {
    return number_format($amount, 2, '.', ',') . ' ' . $currency;
}

/**
 * دالة لتحويل الأرقام إلى كلمات بالعربية
 */
function numberToArabicWords($number) {
    // تنفيذ مبسط - يمكن تطويره أكثر
    $ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    $tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    $hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    
    // تنفيذ مبسط للأرقام الصغيرة
    if ($number < 10) {
        return $ones[$number];
    }
    
    return (string)$number; // إرجاع الرقم كما هو للتنفيذ المبسط
}

// تحميل ملفات التكوين الإضافية إذا كانت موجودة
if (file_exists(__DIR__ . '/local_config.php')) {
    require_once __DIR__ . '/local_config.php';
}
?>
