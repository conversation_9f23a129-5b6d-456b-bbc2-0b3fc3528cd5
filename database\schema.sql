-- نظام إدارة فيش الراتب الجزائري
-- Algerian Payroll Management System Database Schema
-- Created: 2024

-- إعدادات قاعدة البيانات
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- جدول الشركات/المؤسسات
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(255) NOT NULL COMMENT 'اسم الشركة بالعربية',
    name_fr VARCHAR(255) NOT NULL COMMENT 'اسم الشركة بالفرنسية',
    address_ar TEXT COMMENT 'العنوان بالعربية',
    address_fr TEXT COMMENT 'العنوان بالفرنسية',
    commercial_register VARCHAR(50) COMMENT 'رقم السجل التجاري',
    tax_id VARCHAR(50) COMMENT 'رقم التعريف الجبائي',
    cnas_number VARCHAR(50) COMMENT 'رقم الضمان الاجتماعي',
    cnr_number VARCHAR(50) COMMENT 'رقم صندوق التقاعد',
    phone VARCHAR(20),
    email VARCHAR(100),
    logo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الأقسام/الإدارات
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    description_ar TEXT,
    description_fr TEXT,
    manager_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- جدول المناصب/الوظائف
CREATE TABLE positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    title_ar VARCHAR(100) NOT NULL,
    title_fr VARCHAR(100) NOT NULL,
    grade_level INT COMMENT 'مستوى الدرجة',
    base_salary DECIMAL(12,2) DEFAULT 0,
    description_ar TEXT,
    description_fr TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);

-- جدول الموظفين
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'رقم التسجيل',
    first_name_ar VARCHAR(100) NOT NULL,
    last_name_ar VARCHAR(100) NOT NULL,
    first_name_fr VARCHAR(100),
    last_name_fr VARCHAR(100),
    birth_date DATE NOT NULL,
    birth_place_ar VARCHAR(100),
    birth_place_fr VARCHAR(100),
    gender ENUM('M', 'F') NOT NULL,
    marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single',
    children_count INT DEFAULT 0,
    national_id VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم بطاقة الهوية',
    social_security_number VARCHAR(20) UNIQUE COMMENT 'رقم الضمان الاجتماعي',
    address_ar TEXT,
    address_fr TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    hire_date DATE NOT NULL,
    position_id INT NOT NULL,
    status ENUM('active', 'inactive', 'terminated', 'retired') DEFAULT 'active',
    bank_account VARCHAR(50) COMMENT 'رقم الحساب البنكي',
    bank_name VARCHAR(100) COMMENT 'اسم البنك',
    photo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (position_id) REFERENCES positions(id)
);

-- جدول أنواع المنح والتعويضات
CREATE TABLE allowance_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    is_taxable BOOLEAN DEFAULT TRUE COMMENT 'خاضع للضريبة',
    is_social_security_subject BOOLEAN DEFAULT TRUE COMMENT 'خاضع للضمان الاجتماعي',
    calculation_type ENUM('fixed', 'percentage', 'days', 'hours') DEFAULT 'fixed',
    default_amount DECIMAL(12,2) DEFAULT 0,
    description_ar TEXT,
    description_fr TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أنواع الاستقطاعات
CREATE TABLE deduction_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    category ENUM('social_security', 'tax', 'other') NOT NULL,
    calculation_type ENUM('fixed', 'percentage') DEFAULT 'percentage',
    rate DECIMAL(5,4) DEFAULT 0 COMMENT 'معدل الاستقطاع',
    max_amount DECIMAL(12,2) COMMENT 'الحد الأقصى للاستقطاع',
    min_amount DECIMAL(12,2) COMMENT 'الحد الأدنى للاستقطاع',
    is_employer_contribution BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فترات الراتب
CREATE TABLE payroll_periods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    year INT NOT NULL,
    month INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    working_days INT NOT NULL,
    working_hours INT NOT NULL,
    status ENUM('draft', 'processing', 'completed', 'closed') DEFAULT 'draft',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE KEY unique_period (company_id, year, month)
);

-- جدول الرواتب الرئيسية
CREATE TABLE payrolls (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    payroll_period_id INT NOT NULL,
    base_salary DECIMAL(12,2) NOT NULL,
    gross_salary DECIMAL(12,2) NOT NULL COMMENT 'الراتب الإجمالي',
    taxable_income DECIMAL(12,2) NOT NULL COMMENT 'الدخل الخاضع للضريبة',
    social_security_base DECIMAL(12,2) NOT NULL COMMENT 'أساس الضمان الاجتماعي',
    total_allowances DECIMAL(12,2) DEFAULT 0,
    total_deductions DECIMAL(12,2) DEFAULT 0,
    net_salary DECIMAL(12,2) NOT NULL COMMENT 'الراتب الصافي',
    days_worked INT NOT NULL,
    hours_worked INT NOT NULL,
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    absence_days INT DEFAULT 0,
    status ENUM('draft', 'calculated', 'approved', 'paid') DEFAULT 'draft',
    calculated_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (payroll_period_id) REFERENCES payroll_periods(id),
    UNIQUE KEY unique_employee_period (employee_id, payroll_period_id)
);

-- جدول تفاصيل المنح
CREATE TABLE payroll_allowances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payroll_id INT NOT NULL,
    allowance_type_id INT NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    calculation_base DECIMAL(12,2),
    rate DECIMAL(5,4),
    quantity DECIMAL(8,2) DEFAULT 1,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payroll_id) REFERENCES payrolls(id) ON DELETE CASCADE,
    FOREIGN KEY (allowance_type_id) REFERENCES allowance_types(id)
);

-- جدول تفاصيل الاستقطاعات
CREATE TABLE payroll_deductions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payroll_id INT NOT NULL,
    deduction_type_id INT NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    calculation_base DECIMAL(12,2),
    rate DECIMAL(5,4),
    employer_contribution DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payroll_id) REFERENCES payrolls(id) ON DELETE CASCADE,
    FOREIGN KEY (deduction_type_id) REFERENCES deduction_types(id)
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name_ar VARCHAR(100) NOT NULL,
    full_name_fr VARCHAR(100),
    role ENUM('admin', 'hr_manager', 'accountant', 'employee') DEFAULT 'employee',
    company_id INT,
    employee_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- جدول سجل العمليات
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إنشاء الفهارس
CREATE INDEX idx_employees_number ON employees(employee_number);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_payrolls_period ON payrolls(payroll_period_id);
CREATE INDEX idx_payrolls_employee ON payrolls(employee_id);
CREATE INDEX idx_payrolls_status ON payrolls(status);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_table ON audit_logs(table_name, record_id);

SET FOREIGN_KEY_CHECKS = 1;
